# 达人详情页 2.0 需求文档

## 1. 页面顶部 - 达人数据总览卡片

### 1.1 功能描述
页面顶部固定展示达人的数据总览卡片，该卡片不随标签页切换而变化，始终保持在页面最上方。

### 1.2 展示内容

#### 1.2.1 基本信息
- **达人头像**：展示达人的头像图片
- **达人名字**：显示达人的真实姓名或昵称
- **地区和语言**：展示达人所在地区和使用的语言
- **访问链接**：提供达人社交媒体等相关链接

#### 1.2.2 核心数据指标

**粉丝数**
- 数据来源：platforms表中的粉丝数字段
- 展示格式：格式化显示粉丝数量（如：1.2M, 500K等）

**最近发布时间**
- 数据来源：content表
- 计算逻辑：查询content表中最新一条内容的发布时间，计算距今天数
- 展示格式：X天前

**最近推广时间**
- 数据来源：content表
- 计算逻辑：检测content表中推广标注为1的内容，取最新的推广内容发布时间，计算距今天数
- 展示格式：X天前
- 备注：如无推广内容则显示"暂无推广"

**网红综合评分**
- 数据来源：直接读取数据表中的综合评分字段
- 展示格式：数值评分（如：8.5/10）
- 备注：具体评分规则详见下方评分部分

**合作指数**
- 数据来源：根据标签计算得出
- 展示格式：数值评分
- 备注：具体计算规则详见下方合作指数部分

### 1.3 技术实现要点
- 卡片采用固定定位，不随页面滚动
- 数据实时更新，确保信息准确性
- 响应式设计，适配不同屏幕尺寸

## 2. 数据总览页签

### 2.1 基本数据部分

#### 2.1.1 平均观看量
- **数据来源**：最近5期视频的观看量
- **计算逻辑**：计算最近5期视频的观看量均值
- **展示格式**：数值显示（如：1.2M, 500K等）

#### 2.1.2 平均互动量
- **数据来源**：最近5期视频的互动量
- **计算逻辑**：计算最近5期视频的互动量均值
- **展示格式**：数值显示（如：50K, 100K等）

#### 2.1.3 内容数量
- **数据来源**：content表
- **计算逻辑**：统计content表中该网红ID对应的视频总数
- **展示格式**：整数显示（如：158个视频）

#### 2.1.4 观看量/粉丝数
- **数据来源**：平均观看量和粉丝数
- **计算逻辑**：平均观看量 ÷ 粉丝数 × 100%
- **展示格式**：百分比显示（如：15.2%）

#### 2.1.5 预计曝光量
- **数据来源**：平均播放量
- **计算逻辑**：平均播放量 × 90%
- **展示格式**：数值显示（如：1.08M）

### 2.2 增长数据部分

#### 2.2.1 粉丝数
- **数据来源**：platforms表的粉丝数量字段
- **计算逻辑**：直接读取platforms表中该网红的当前粉丝数
- **展示格式**：数值显示（如：1.2M, 500K等）

#### 2.2.2 观看量
- **数据来源**：content表中该网红所有视频的观看量
- **计算逻辑**：累加content表中该网红ID对应的所有视频观看量
- **展示格式**：数值显示（如：10.5M, 2.3B等）

#### 2.2.3 发布量
- **数据来源**：content表的内容发布日期
- **计算逻辑**：累加统计到指定日期该网红的作品总数
  - 按发布日期排序，计算每日累计发布量
  - 例如：1号发布1个视频累计为1，2号发布1个视频累计为2，3号发布1个视频累计为3
- **展示格式**：整数显示（如：158个作品）

#### 2.2.4 增长趋势曲线
- **数据来源**：基于历史数据的增长率计算
- **计算逻辑**：
  - 三条曲线分别对应粉丝数、观看量、发布量的增长趋势
  - 以第一天数据为基准，计算后续增长率
  - 使用随机斜率进行数据拟合，斜率范围：0.8-0.9之间
- **展示格式**：折线图形式展示增长趋势
- **技术实现**：使用假数据拟合算法生成平滑增长曲线

### 2.3 频道质量评分部分

#### 2.3.1 评分规则体系
**评分制度**：5分满分制，只能整数分
**排名分布评分标准**：
- 前10%：5分
- 前30%：4分
- 前50%：3分
- 前80%：2分
- 其他：1分

#### 2.3.2 粉丝增长评分（按排名评分）
- **数据来源**：platforms表的粉丝数
- **计算逻辑**：
  - 由于是静态demo，生成假数据：随机生成0.5%-5%的月增长率
  - 计算公式：月增长率 = 随机数(0.5%, 5%)
- **评分规则**：按排名分布评分标准评分

#### 2.3.3 创作频率评分（按排名评分）
- **数据来源**：content表的发布时间
- **计算逻辑**：统计最近30天发布内容数量
- **评分规则**：按排名分布评分标准评分

#### 2.3.4 互动率评分（按排名评分）
- **数据来源**：content表的互动量和观看量
- **计算逻辑**：(近期点赞数+评论数+分享数)/近期观看量总和
- **评分规则**：按排名分布评分标准评分

#### 2.3.5 频道质量评分（大模型评分）
- **数据来源**：content表的内容数据
- **计算逻辑**：大模型分析内容质量，直接给出1-5分
- **大模型评分要求**：
  - 分析内容创意度、制作质量、信息价值
  - 考虑视频时长合理性、内容完整度
  - 直接输出1-5分整数评分

#### 2.3.6 粉丝可信度评分（大模型评分）
- **数据来源**：content表的评论数据
- **计算逻辑**：大模型分析评论质量，直接给出1-5分
- **大模型评分要求**：
  - 分析评论的真实性、多样性、互动质量
  - 识别机器人评论、重复评论等异常行为
  - 直接输出1-5分整数评分

#### 2.3.7 综合评分计算
- **计算公式**：(粉丝增长 + 创作频率 + 互动率 + 频道质量 + 粉丝可信度) ÷ 5
- **结果处理**：四舍五入到整数，最终得分1-5分
- **数据存储**：各维度评分及综合评分存储到platforms表中
- **展示格式**：五角星评分和雷达图形式展示各维度评分

**评级标准**：
- **4.0-5.0分**：优秀 - 综合表现卓越，合作价值极高
- **3.5-3.9分**：良好 - 表现出色，具有较高合作潜力
- **3.0-3.4分**：中等 - 表现尚可，有一定合作价值
- **2.0-2.9分**：一般 - 表现平平，需谨慎考虑合作
- **1.0-1.9分**：较差 - 表现不佳，不建议合作

**卡片展示格式**：
- **分数显示**：X.X/5.0 格式（如：4.2/5.0）
- **文本描述**：换行显示对应评级文本
- **示例**：
  ```
  4.2/5.0
  优秀 - 综合表现卓越，合作价值极高
  ```

**颜色编码**：
- **优秀（4.0-5.0）**：深绿色
- **良好（3.5-3.9）**：绿色
- **中等（3.0-3.4）**：黄色
- **一般（2.0-2.9）**：橙色
- **较差（1.0-1.9）**：红色

**字段设计**：
- **粉丝增长评分**：follower_growth_score (INT 1-5)
- **创作频率评分**：content_frequency_score (INT 1-5)
- **互动率评分**：engagement_rate_score (INT 1-5)
- **频道质量评分**：channel_quality_score (INT 1-5)
- **粉丝可信度评分**：follower_credibility_score (INT 1-5)
- **综合评分**：total_quality_score (INT 1-5)
- **存储位置**：platforms表
- **更新机制**：当相关数据变化时重新计算并更新各字段
- **读取说明**：其他页面可直接读取这些字段展示达人评分

### 2.4 合作倾向评分部分

#### 2.4.1 内容更新频率（时效性）
- **数据来源**：content表的最新发布时间
- **计算逻辑**：查询content表中该达人最后发布内容的日期，计算距离当前时间的天数
- **评分规则**：
  - **0-3天有内容更新**：3分
  - **4-14天有内容更新**：2分  
  - **15-30天有内容更新**：1分
  - **超过30天未更新**：0分

#### 2.4.2 内容产量（活跃度）
- **数据来源**：content表近30天的发布数量
- **评分规则**：
  - **近30天内更新15+内容**：4分
  - **近30天内更新10-14内容**：3分
  - **近30天内更新5-9内容**：2分
  - **近30天内更新1-4内容**：1分
  - **近30天内无内容更新**：0分

#### 2.4.3 联系便利性（沟通成本）
- **数据来源**：达人的联系方式信息和历史沟通记录
- **评分规则**：
  - **有联系方式 + 半年内回复过邮件**：3分
  - **仅有联系方式**：2分
  - **无任何联系方式**：0分

#### 2.4.4 合作倾向总分计算
- **计算公式**：内容更新频率分数 + 内容产量分数 + 联系便利性分数
- **分数范围**：0-10分（原始分数）
- **数据存储**：计算完成后存储到platforms表的cooperation_score字段中
- **展示格式**：数值显示 + 评级标识

**评级标准**：
- **9-10分**：良好 - 较为明显的合作潜力与意向
- **7-8分**：及格 - 通常活跃，有一定合作意向
- **5-6分**：一般 - 合作意向不明显
- **3-4分**：较低 - 合作概率相对较低
- **0-2分**：差 - 合作概率相对较低

**卡片展示格式**：
- **分数显示**：X/10 格式（如：2/10）
- **文本描述**：换行显示对应评级文本
- **示例**：
  ```
  2/10
  差 - 合作概率相对较低
  ```

**字段设计**：
- **字段名**：cooperation_score
- **数据类型**：INT (0-10)
- **存储位置**：platforms表
- **更新机制**：当达人内容更新或联系方式变化时重新计算并更新
- **读取说明**：其他页面可直接读取此字段展示合作倾向评分

#### 2.4.5 合作行为标签详细展示
- **数据来源**：platforms表的collaborationLabels字段
- **标签生成**：根据合作倾向评分的3个维度自动生成对应标签
- **标签类型**：
  - **内容更新频率标签**：
    - "0-3天有内容更新"
    - "4-14天有内容更新"
    - "15-30天有内容更新"
    - "超过30天未更新"
  - **内容产量标签**：
    - "近30天内更新15+内容"
    - "近30天内更新10-14内容"
    - "近30天内更新5-9内容"
    - "近30天内更新1-4内容"
    - "近30天内无内容更新"
  - **联系便利性标签**：
    - "有联系方式"
    - "半年内回复过邮件"
    - "无联系方式"
- **展示位置**：在合作倾向评分下方以标签形式展示
- **颜色编码**：
  - 绿色：积极正面标签（高分标签）
  - 灰色：消极标签（低分标签）
- **标签排序**：按照标签对合作倾向的影响程度排序
- **标签更新**：与合作倾向评分同步更新

### 2.5 合作价格部分

#### 2.5.1 CPM价格获取
- **数据来源**：CPM查询表
- **查询维度**：
  - **行业**：从platforms表的行业分类字段读取
  - **粉丝数范围**：根据platforms表的subscribers字段确定范围区间
  - **地区**：从platforms表的地区字段读取
  - **平台**：从platforms表的platform字段读取
- **计算逻辑**：根据这4个维度在CPM查询表中匹配获取对应的CPM价格范围
- **存储字段**：将CPM价格范围的中位数存储到platforms表的estimatedCPM字段

#### 2.5.2 植入视频价格范围计算
- **数据来源**：
  - CPM价格范围：从CPM查询表获取的价格范围（如：$20-$40）
  - 粉丝数：platforms表的subscribers字段
- **计算逻辑**：
  - 最低报价 = CPM最低价 × 粉丝数 ÷ 1000
  - 最高报价 = CPM最高价 × 粉丝数 ÷ 1000
  - 价格范围 = [最低报价, 最高报价]
- **存储字段**：将计算结果存储到platforms表的estimatedVideoPrice字段
- **展示格式**：价格区间形式（如：$500-$1500）

#### 2.5.3 价格更新机制
- **更新频率**：每周更新一次
- **更新流程**：
  1. 从CPM查询表获取最新CPM价格范围
  2. 计算CPM中位数并存储到estimatedCPM字段
  3. 根据CPM价格范围重新计算植入视频价格范围
  4. 更新platforms表中的estimatedVideoPrice字段

#### 2.5.4 价格展示规范
- **货币符号**：统一使用美元符号"$"
- **数值简写**：超过1000时使用"k"简写（如：$1.5k）
- **区间格式**：始终显示最小值-最大值

### 2.6 排名展示部分

#### 2.6.1 粉丝数行业排名
- **数据来源**：platforms表的industryRank字段
- **计算逻辑**：根据达人所在行业的粉丝数排名计算百分比位置
- **展示格式**：百分比形式（如：粉丝数行业排名：1%）
- **说明**：显示该达人在其所属行业内的粉丝数排名百分比

#### 2.6.2 所在国家排名
- **数据来源**：platforms表的countryRank字段
- **计算逻辑**：根据达人所在国家的粉丝数排名计算百分比位置
- **展示格式**：百分比形式（如：所在国家排名：5%）

## 3. 受众数据页签

### 3.1 基本数据部分

#### 3.1.1 粉丝可信度评分
- **数据来源**：大模型分析粉丝行为数据
- **计算逻辑**：大模型分析粉丝增长模式、互动质量、评论真实性等维度
- **评分要求**：
  - 分析粉丝增长是否自然
  - 识别机器人粉丝和虚假互动
  - 评估评论质量和真实性
  - 检测异常增长模式
  - 直接输出1-5分整数评分
- **存储字段**：platforms表的follower_credibility_score字段
- **字段格式**：INT(1-5)
- **展示格式**：1-5分评分

#### 3.1.2 受众区域分布
- **数据来源**：大模型分析受众地理位置数据
- **计算逻辑**：大模型分析受众的地理分布情况
- **分析要求**：
  - 分析受众主要来源地区
  - 计算各地区受众百分比
  - 识别核心受众市场
  - 评估地区分布的合理性
- **存储字段**：platforms表的geoDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "北美": 45.2,
    "欧洲": 28.5,
    "亚洲": 18.3,
    "南美": 5.8,
    "其他": 2.2
  }
  ```
- **展示格式**：地图或饼图形式展示各地区百分比

#### 3.1.3 最多受众性别
- **数据来源**：大模型分析受众性别数据
- **计算逻辑**：大模型分析受众的性别分布情况
- **分析要求**：
  - 分析受众性别比例
  - 识别主要受众性别
  - 评估性别分布特征
- **存储字段**：platforms表的genderDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "男性": 42.0,
    "女性": 58.0
  }
  ```
- **展示格式**：文本 + 百分比显示

#### 3.1.4 最多受众年龄
- **数据来源**：大模型分析受众年龄数据
- **计算逻辑**：大模型分析受众的年龄分布情况
- **分析要求**：
  - 分析受众年龄分布
  - 识别主要受众年龄段
  - 评估年龄分布特征
- **存储字段**：platforms表的ageDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "13-17": 8.2,
    "18-24": 42.3,
    "25-34": 28.5,
    "35-44": 15.7,
    "45-54": 3.8,
    "55+": 1.5
  }
  ```
- **展示格式**：文本 + 百分比显示

#### 3.1.5 基本数据卡片展示
- **卡片布局**：4个卡片横向排列（audience-overview-grid）
- **卡片结构**：
  - 图标区域：使用FontAwesome图标
  - 数据区域：显示主要数值和百分比
  - 状态区域：显示评级或描述
- **卡片样式**：
  - 粉丝可信度：星形图标，显示评分和等级
  - 受众区域：地图图标，显示主要地区
  - 受众性别：性别图标，显示主要性别和百分比
  - 受众年龄：生日蛋糕图标，显示主要年龄段和百分比

#### 3.1.6 受众语言分布
- **数据来源**：大模型分析受众语言数据
- **计算逻辑**：大模型分析受众的语言分布情况
- **分析要求**：
  - 分析受众主要使用语言
  - 计算各语言使用者百分比
  - 识别核心语言市场
- **存储字段**：platforms表的languageDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "英语": 84.5,
    "西班牙语": 7.3,
    "葡萄牙语": 6.4,
    "其他语言": 1.8
  }
  ```
- **展示格式**：在受众特征部分显示

### 3.2 受众特征部分

#### 3.2.1 受众区域分布展示
- **展示方式**：横向条形图 + 详细列表
- **排序规则**：按百分比从高到低排序
- **数据来源**：platforms表的geoDistribution字段
- **展示内容**：地区名称、百分比、可视化条形图

#### 3.2.2 受众语言分布展示
- **展示方式**：横向条形图 + 详细列表
- **排序规则**：按百分比从高到低排序
- **数据来源**：platforms表的languageDistribution字段
- **展示内容**：语言名称、百分比、可视化条形图

#### 3.2.3 性别分布展示
- **展示方式**：圆环图（甜甜圈图）
- **数据来源**：platforms表的genderDistribution字段
- **展示内容**：男性、女性两个分类的百分比
- **图表特点**：二分性别展示，中心显示总计100%

#### 3.2.4 年龄分布展示
- **展示方式**：条形图
- **数据来源**：platforms表的ageDistribution字段
- **年龄分段**：13-17, 18-24, 25-34, 35-44, 45-54, 55-64, 65+
- **展示内容**：各年龄段的百分比分布

### 3.3 营销分析部分

#### 3.3.1 受众反馈指标（2个百分比指标）

**正向反馈百分比**
- **数据来源**：大模型分析评论内容的情感倾向
- **计算逻辑**：统计正面评论占总评论数的百分比
- **存储字段**：platforms表的positiveFeedbackRatio字段
- **字段格式**：Float (0-100)
- **展示格式**：百分比显示（如：61%）

**内容感兴趣百分比**
- **数据来源**：大模型分析推广内容的受众反馈
- **计算逻辑**：统计对推广内容感兴趣的评论占总评论数的百分比
- **存储字段**：platforms表的promoInterestRatio字段
- **字段格式**：Float (0-100)
- **展示格式**：百分比显示（如：60%）

#### 3.3.2 消费影响力指标（2个评分指标）

**推广吸引度**
- **数据来源**：大模型分析推广内容的吸引力
- **计算逻辑**：大模型综合分析推广内容的互动表现、评论质量等
- **评分范围**：0-5分，每0.5分一个档位
- **存储字段**：platforms表的promoAttractionScore字段
- **字段格式**：Integer (0-10，实际显示时除以2)
- **展示格式**：小数显示（如：3.5分）

**推广专业度**
- **数据来源**：大模型分析推广内容的专业性
- **计算逻辑**：大模型评估推广内容与达人专业领域的匹配度
- **评分范围**：0-5分，每0.5分一个档位
- **存储字段**：platforms表的promoProfessionalScore字段
- **字段格式**：Integer (0-10，实际显示时除以2)
- **展示格式**：小数显示（如：1.5分）

#### 3.3.3 营销分析文本展示规范

**正向反馈百分比文本模板**
- **好（>50%）**："{达人名称}的内容广受欢迎，有高达{百分比}的观众表达了积极的评价，具有极佳的受众吸引力和满意度。"
- **中（30-50%）**："{达人名称}的内容获得了较为积极的反馈，{百分比}的观众给出了正面评价，整体表现良好。"
- **差（<30%）**："{达人名称}的内容反馈一般，仅有{百分比}的观众给出了正面评价，需要进一步提升内容质量。"

**内容感兴趣百分比文本模板**
- **好（>50%）**："{达人名称}对推广产品的呈现方式获得了观众的一致好评，正面评论的比例高达{百分比}，这个频道的品牌推广效果极为出色，受众对此表示极高的认可。"
- **中（30-50%）**："{达人名称}的推广内容获得了不错的反响，{百分比}的观众对推广产品表示感兴趣，推广效果较为理想。"
- **差（<30%）**："{达人名称}的推广内容关注度不高，仅有{百分比}的观众对推广产品表示感兴趣，推广效果有待提升。"

**推广吸引度文本模板**
- **好（>3.5分）**："{达人名称}的推广内容具有出色的吸引力，吸引度指数达到{分数}，推广内容与自然内容的互动表现基本一致，合作价值很高。"
- **中（2.0-3.5分）**："{达人名称}的推广内容吸引度中等，吸引度指数为{分数}，推广效果尚可，有一定的合作潜力。"
- **差（<2.0分）**："{达人名称}的推广内容似乎并未达到预期效果，吸引度指数仅为{分数}，相比自然内容，互动量水平并不符合预期。如果合作，需要投入更多的精力到内容创意上。"

**推广专业度文本模板**
- **好（>3.5分）**："{达人名称}在推广领域表现专业，专业指数达到{分数}，推广内容与其专业领域高度匹配，合作效果值得期待。"
- **中（2.0-3.5分）**："{达人名称}的推广专业度尚可，专业指数为{分数}，推广内容基本符合其专业领域，合作效果一般。"
- **差（<2.0分）**："{达人名称}的推广专业度有待提高，专业指数仅为{分数}。其推广内容可能并非其专业领域，创作主题不够专一，需调整推广策略以提升其专业性。"

### 3.4 内容与兴趣部分

#### 3.4.1 数据分析和存储

**数据来源**：大模型分析content表中该达人的所有内容
**计算逻辑**：
- 大模型分析视频内容自动分类到预设兴趣类别
- 统计各类别视频数量和占比
- 选择占比最高的前5个类别展示
- 其余类别合并为"其他"

**存储字段**：platforms表的contentDistribution字段
**字段格式**：JSON

#### 3.4.2 页面布局规范

**整体布局**：
- 左侧：圆环图区域（占宽度40%）
- 右侧：表格区域（占宽度60%）
- 两个区域并排展示，响应式适配

#### 3.4.3 左侧圆环图展示规则

**圆环图规格**：
- 图表类型：甜甜圈图（圆环图）
- 显示数据：前5个类别 + 其他类别（如有）
- 圆环粗细：中等粗细，中心留白50%
- 颜色方案：使用6种不同颜色区分类别

**图表标注**：
- 中心文字：显示"内容分布"和"Top 5 分类"
- 鼠标悬停：显示类别名称和百分比
- 图例：不显示独立图例

#### 3.4.4 右侧表格展示规则

**表格结构**：
- 表头：兴趣点 | 描述 | 百分比
- 列宽比例：1:3:1
- 固定显示5行数据

**表格内容**：
- 兴趣点列：显示类别名称
- 描述列：显示完整的描述文本
- 百分比列：显示百分比数值 + 进度条

**进度条规则**：
- 每行百分比下方显示横向进度条
- 进度条长度对应百分比数值
- 使用渐变色彩填充

#### 3.4.5 描述文本生成规则

**展示规则**：始终显示5行文本，每行对应一个类别

**固定文本模板**：
```
{网红名}的{类别1}内容占其整个账户的{百分比1}%，展现了其在该领域的专业程度和受众喜爱。
{网红名}在{类别2}领域的内容占比为{百分比2}%，体现了其多元化的内容创作能力。
{网红名}的{类别3}相关视频占总内容的{百分比3}%，吸引了大量相关爱好者的关注。
{网红名}制作的{类别4}内容占比达到{百分比4}%，显示了其在该领域的活跃度。
{网红名}的{类别5}视频内容占其账户的{百分比5}%，丰富了频道的内容多样性。
```

**实际展示示例**：
```
MrBeast的汽车内容占其整个账户的32%，展现了其在该领域的专业程度和受众喜爱。
MrBeast在手机游戏领域的内容占比为18%，体现了其多元化的内容创作能力。
MrBeast的宠物&动物相关视频占总内容的17%，吸引了大量相关爱好者的关注。
MrBeast制作的表演&喜剧内容占比达到17%，显示了其在该领域的活跃度。
MrBeast的vlog视频内容占其账户的17%，丰富了频道的内容多样性。
```

**数据更新**：每月更新一次，基于最近90天的视频内容统计

### 3.5 粉丝可信度部分

#### 3.5.1 数据分析和计算

**数据来源**：大模型分析content表中该达人的评论数据和粉丝行为数据
**计算逻辑**：
- 大模型分析粉丝互动行为、评论质量、账户活跃度等
- 将粉丝分类为4种类型：普通粉、可疑粉、僵尸粉、网红粉
- 计算各类型粉丝的占比
- 计算真实受众比例（普通粉 + 网红粉）

**存储字段**：
- platforms表的followerTypeDistribution字段：存储四种粉丝类型占比
- platforms表的realFollowerRatio字段：存储真实受众比例

**字段格式**：
- followerTypeDistribution：JSON格式
- realFollowerRatio：Float格式

**数据格式**：
```json
{
  "普通粉": 76.8,
  "可疑粉": 14.3,
  "僵尸粉": 6.2,
  "网红粉": 2.7
}
```

#### 3.5.2 页面布局规范

**整体布局**：
- 标题区域：显示"粉丝可信度"标题和图标
- 卡片区域：两个并排的卡片
  - 左侧：受众类型卡片
  - 右侧：真实受众卡片

#### 3.5.3 受众类型卡片展示规则

**卡片结构**：
- 卡片标题：显示"受众类型"
- 图表区域：圆形饼图
- 图例区域：四种粉丝类型的详细列表
- 说明文本区域

**圆形饼图规格**：
- 图表类型：pie图（完整圆形）
- 显示数据：四种粉丝类型占比
- 颜色方案：
  - 普通粉：蓝色（#4285F4）
  - 可疑粉：橙色（#FF9800）
  - 僵尸粉：绿色（#4CAF50）
  - 网红粉：紫色（#9C27B0）

**图例列表规格**：
- 每行显示：颜色条 + 类型名称 + 百分比
- 颜色条：水平条形图，长度对应占比
- 排序：按占比从高到低排列

**说明文本**：
"受众类型中，真实用户为两种属性，普通用户粉丝和有影响力的网红关注者，虚假受众为有可疑点赞评论行为的帐户和大量关注取关账户"

#### 3.5.4 真实受众卡片展示规则

**卡片结构**：
- 卡片标题：显示"真实受众"
- 图表区域：圆环图
- 描述区域：数据说明
- 说明文本区域

**圆环图规格**：
- 图表类型：doughnut图（圆环图）
- 圆环粗细：中心留白70%
- 显示数据：真实受众比例 vs 非真实受众比例
- 颜色方案：真实受众为蓝色，非真实受众为灰色

**中心数据显示**：
- 中心显示真实受众百分比（如：79.5%）
- 下方显示"真实"标签

**描述文本格式**：
"网红@{达人名称} 的真实受众为{真实比例}%"

**说明文本**：
"网红频道的真实受众关注者"

## 4. 内容数据页签

### 4.1 基本数据部分

#### 4.1.1 数据指标定义

**互动率**
- **数据来源**：content表的互动数据
- **计算逻辑**：(点赞数 + 评论数 + 分享数) ÷ 观看量 × 100%
- **展示格式**：百分比显示（如：6.13%）
- **评级规则**：基于数据库中同平台达人互动率排名百分比
  - 优秀：排名前30%
  - 及格：排名30%-70%
  - 较差：排名后30%

**观看量/粉丝数**
- **数据来源**：content表的观看量和platforms表的粉丝数
- **计算逻辑**：近30天平均观看量 ÷ 粉丝数 × 100%
- **展示格式**：百分比显示（如：94.85%）
- **评级规则**：基于数据库中同平台达人观看率排名百分比
  - 优秀：排名前30%
  - 及格：排名30%-70%
  - 较差：排名后30%

**点赞数/观看数**
- **数据来源**：content表的点赞数和观看数
- **计算逻辑**：近30天平均点赞数 ÷ 平均观看数 × 100%
- **展示格式**：百分比显示（如：5.47%）
- **评级规则**：基于数据库中同平台达人点赞率排名百分比
  - 优秀：排名前30%
  - 及格：排名30%-70%
  - 较差：排名后30%

**评论数/观看数**
- **数据来源**：content表的评论数和观看数
- **计算逻辑**：近30天平均评论数 ÷ 平均观看数 × 100%
- **展示格式**：百分比显示（如：0.04%）
- **评级规则**：基于数据库中同平台达人评论率排名百分比
  - 优秀：排名前30%
  - 及格：排名30%-70%
  - 较差：排名后30%

#### 4.1.2 排名计算规范

**排名计算说明**：
- 从数据库查询同平台所有达人对应指标
- 计算当前达人在所有达人中的排名位置
- 转换为百分比形式（如：排名第100位/总1000人 = 前10%）
- 根据百分比位置确定评级

**排名存储字段**（platforms表）：
- engagementRateRank：互动率排名百分位（1-100）
- viewsPerFollowerRank：观看率排名百分位（1-100）
- likesPerViewRank：点赞率排名百分位（1-100）
- commentsPerViewRank：评论率排名百分位（1-100）

#### 4.1.3 数据存储规范

**数值存储字段**（platforms表）：
- engagementRate30d：近30天互动率
- viewsPerFollowerRatio：观看量/粉丝数比例
- likesPerView30d：点赞/观看比例
- commentsPerView30d：评论/观看比例

**字段格式**：Float类型，保留2位小数

#### 4.1.4 展示规范

**卡片布局**：
- 使用4个独立的指标卡片
- 网格布局，2×2或1×4排列
- 每个卡片包含：图标、标题、数值、评级文字、小型圆环图

**圆环图规格**：
- 类型：doughnut图（小型）
- 尺寸：80×80像素
- 中心留白：70%
- 显示当前数值在排名中的位置
- 颜色根据评级动态变化：
  - 优秀：绿色（#34A853）
  - 及格：橙色（#FF9800）
  - 较差：红色（#EA4335）

**评级文字显示**：
- 在数值下方显示评级文字（优秀/及格/较差）
- 文字颜色与圆环图颜色一致

**数据更新**：
- 数值更新：每日更新
- 排名更新：每周更新一次
- 计算周期：基于最近30天的内容数据

### 4.2 发布分析部分

#### 4.2.1 发布时间日历

**功能描述**：
以简单日历形式展示达人的内容发布情况，标记有发布内容的日期

**数据来源**：content表的publishDate字段

**计算逻辑**：
- 统计时间范围：最近90天
- 识别有发布内容的日期
- 直接从content表查询该达人的发布日期

#### 4.2.2 发布频率展示

**功能描述**：
展示达人在一周中各天的发布习惯，识别发布高峰期

**数据来源**：content表的publishDate字段

**计算逻辑**：
- 统计时间范围：最近90天
- 按星期几（周一至周日）汇总发布数量
- 计算每天的平均发布量

**数据存储**：
- 字段名：publishFrequencyWeek（platforms表）
- 格式：JSON
- 数据结构：
```json
{
  "周一": 2.5,
  "周二": 3.2,
  "周三": 2.8,
  "周四": 4.1,
  "周五": 3.7,
  "周六": 1.9,
  "周日": 2.3
}
```

#### 4.2.3 发布频率四个显示字段

**每周发布**：
- **数据来源**：content表的publishDate字段
- **计算逻辑**：统计最近30天的发布数量 ÷ 4.3（30天约等于4.3周）
- **存储字段**：weeklyPublishCount（platforms表）
- **字段格式**：Integer

**每月发布**：
- **数据来源**：content表的publishDate字段
- **计算逻辑**：统计最近30天的总发布数量
- **存储字段**：monthlyPublishCount（platforms表）
- **字段格式**：Integer

**最近发布时间**：
- **数据来源**：content表的publishDate字段
- **计算逻辑**：查询该达人最新一条内容的发布时间
- **存储字段**：lastPublishTime（platforms表）
- **字段格式**：Date

**发布频率等级**：
- **数据来源**：使用与数据总览页签中创作频率评分相同的数据来源
- **计算逻辑**：
  - 统计最近30天发布内容数量
  - 按数据库中同平台达人创作频率排名百分比评级
  - 排名前30%：优秀
  - 排名30%-70%：良好  
  - 排名后30%：一般
- **存储字段**：content_frequency_score（platforms表，与频道质量评分共用）
- **字段格式**：Integer (1-5分)

## 4.3 频道标签

### 4.3.1 功能描述
展示达人频道的全面标签信息，通过组合多个字段生成丰富的标签云，让用户快速了解达人的内容特征、受众特点和合作属性。

### 4.3.2 数据来源

#### 4.3.2.1 Array类型字段（直接使用）
- **enhancedTags**：增强标签（最多3个）
- **promotedProductCategory**：推广商品分类
- **collaborationLabels**：合作相关标签
- **contentFormat**：内容格式
- **contentScene**：内容展示场景

#### 4.3.2.2 String类型字段转标签规则
**基础内容属性**：
- industry → #行业:科技数码
- videoStyle → #风格:专业
- contentTone → #调性:轻松幽默
- language → #语言:英语
- regionCountry → #地区:美国
- categoryDepth → #垂类深度:高
- promotionAbility → #推广能力:强
- brandRepetitionRate → #品牌重复率:高

**受众属性**：
- audienceGender → #受众性别:女性为主
- audienceAge → #受众年龄:25-34岁
- audienceCountry → #粉丝国家:美国
- audienceLanguage → #粉丝语言:英语
- topAudienceCountry → #最多粉丝:巴西
- topAudienceGender → #主要性别:男性
- topAudienceAge → #主要年龄:18-24岁
- audiencePurchasePower → #购买力:高

**商业属性**：
- brandedAdFrequency → #广告频次:频繁

### 4.3.3 字段值转换规则

#### 4.3.3.1 性别转换
```
audienceGender字段：
"male" → "#受众性别:男性为主"
"female" → "#受众性别:女性为主"
"mixed" → "#受众性别:男女均衡"

topAudienceGender字段：
"male" → "#主要性别:男性"
"female" → "#主要性别:女性"
```

#### 4.3.3.2 年龄转换
```
audienceAge/topAudienceAge字段：
"13-17" → "#受众年龄:13-17岁" / "#主要年龄:13-17岁"
"18-24" → "#受众年龄:18-24岁" / "#主要年龄:18-24岁"
"25-34" → "#受众年龄:25-34岁" / "#主要年龄:25-34岁"
"35-44" → "#受众年龄:35-44岁" / "#主要年龄:35-44岁"
"45-54" → "#受众年龄:45-54岁" / "#主要年龄:45-54岁"
"55-64" → "#受众年龄:55-64岁" / "#主要年龄:55-64岁"
"65+" → "#受众年龄:65岁以上" / "#主要年龄:65岁以上"
```

#### 4.3.3.3 购买力转换
```
audiencePurchasePower字段：
"high" → "#购买力:高"
"medium" → "#购买力:中等"
"low" → "#购买力:低"
```

#### 4.3.3.4 推广能力转换
```
promotionAbility字段：
"strong" → "#推广能力:强"
"medium" → "#推广能力:中等"
"weak" → "#推广能力:弱"
```

#### 4.3.3.5 垂类深度转换
```
categoryDepth字段：
"high" → "#垂类深度:高"
"medium" → "#垂类深度:中"
"low" → "#垂类深度:低"
```

#### 4.3.3.6 品牌重复率转换
```
brandRepetitionRate字段：
"high" → "#品牌重复率:高"
"medium" → "#品牌重复率:中"
"low" → "#品牌重复率:低"
```

#### 4.3.3.7 广告频次转换
```
brandedAdFrequency字段：
"frequent" → "#广告频次:频繁"
"moderate" → "#广告频次:适中"
"rare" → "#广告频次:较少"
```

### 4.3.4 展示规则

#### 4.3.4.1 标签数量统计
- **显示总标签数**：格式为"X 个标签"
- **预期数量**：25-30个标签
- **最大显示**：50个标签，超出部分可展开查看

#### 4.3.4.2 词云展示
- **标签大小**：分6个等级（size-1到size-6）
- **核心标签**：enhancedTags使用最大尺寸（size-6）
- **其他标签**：按重要性递减分配尺寸

#### 4.3.4.3 标签分类与颜色
- **内容标签（蓝色）**：contentFormat、videoStyle、contentTone、enhancedTags
- **行业标签（绿色）**：industry、promotedProductCategory、categoryDepth
- **地域标签（橙色）**：regionCountry、language、audienceCountry、audienceLanguage
- **合作标签（紫色）**：collaborationLabels、promotionAbility、brandRepetitionRate
- **受众标签（红色）**：audienceGender、audienceAge、audiencePurchasePower
- **商业标签（灰色）**：brandedAdFrequency

#### ******* 排序规则
1. 优先显示核心标签（enhancedTags）
2. 其次显示行业和内容类标签
3. 最后显示受众和商业标签
4. 同类标签聚集显示

### 4.3.5 数据示例

```javascript
// 完整标签集合示例
[
  // Array字段直接提取
  "#fashion", "#beauty", "#lifestyle", // enhancedTags
  "#电子产品", "#美妆护肤", // promotedProductCategory
  "#回复率高", "#近期活跃", // collaborationLabels
  "#产品评测", "#教程", "#开箱", // contentFormat
  "#室内", "#工作室", // contentScene
  
  // String字段转换
  "#行业:科技数码", // industry
  "#风格:专业", // videoStyle
  "#调性:轻松幽默", // contentTone
  "#语言:英语", // language
  "#地区:美国", // regionCountry
  "#垂类深度:高", // categoryDepth
  "#推广能力:强", // promotionAbility
  "#品牌重复率:高", // brandRepetitionRate
  
  // 受众相关转换
  "#受众性别:女性为主", // audienceGender
  "#受众年龄:25-34岁", // audienceAge
  "#粉丝国家:美国", // audienceCountry
  "#粉丝语言:英语", // audienceLanguage
  "#最多粉丝:巴西", // topAudienceCountry
  "#主要性别:男性", // topAudienceGender
  "#主要年龄:18-24岁", // topAudienceAge
  "#购买力:高", // audiencePurchasePower
  "#广告频次:频繁" // brandedAdFrequency
]
```

### 4.3.6 技术实现要点
- **空值处理**：跳过NULL或空字符串字段
- **去重处理**：合并相同标签，避免重复显示
- **格式统一**：所有标签自动添加"#"前缀
- **交互设计**：支持标签点击复制功能
- **响应式**：适配不同屏幕尺寸的标签布局

## 4.4 网红类别

### 4.4.1 功能描述
通过大模型分析达人所有内容，智能识别并生成网红的主要内容类别及其占比分布。

### 4.4.2 数据来源与生成方式

#### ******* 数据来源
- **主要来源**：content表中该达人的所有内容记录
- **分析字段**：title, description, enhancedTags, contentType, publishDate
- **生成方式**：大模型分析生成
- **存储字段**：contentCategories（platforms表新增字段）

#### ******* 大模型分析逻辑
```
输入数据：
- 达人最近100条内容的标题、描述、标签
- 内容发布时间分布
- 已有的enhancedTags标签

分析任务：
1. 识别内容的主要类别
2. 计算各类别的占比
3. 确保占比总和为100%
4. 最多输出4个主要类别
```

### 4.4.3 字段格式设计

#### ******* 存储格式
```json
// contentCategories字段格式
[
  {"category": "娱乐", "percentage": 48.2},
  {"category": "音乐", "percentage": 24.7},
  {"category": "流行音乐", "percentage": 27.1}
]
```

#### ******* 类别标准化
```
常见类别：
娱乐、音乐、流行音乐、生活方式、孩子、玩具、幽默、科技、
美妆、时尚、游戏、体育、教育、料理、旅行、健身、汽车、
宠物、艺术、新闻、商业、舞蹈、摄影、手工、读书等
```

### 4.4.4 展示规则
- **显示数量**：2-4个主要类别
- **展示格式**：类别名称 + 百分比
- **排序规则**：按占比从高到低排序
- **数据验证**：所有类别百分比总和为100%

### 4.4.5 大模型Prompt设计

#### ******* 网红类别分析Prompt
```
任务：分析网红内容类别分布

输入数据：
- 内容标题列表：{titles}
- 内容描述列表：{descriptions}
- 现有标签：{existing_tags}

分析要求：
1. 识别该网红的主要内容类别
2. 计算各类别占比（总和100%）
3. 最多输出4个类别
4. 使用标准化类别名称

输出格式：
[
  {"category": "类别名称", "percentage": 占比数值},
  ...
]

类别词典：娱乐、音乐、流行音乐、生活方式、孩子、玩具、幽默、科技、美妆、时尚、游戏、体育、教育、料理、旅行、健身、汽车、宠物、艺术、新闻、商业、舞蹈、摄影、手工、读书
```

## 4.5 前5主题标签

### 4.5.1 功能描述
通过大模型分析达人所有内容，提取最具代表性的5个主题标签及其使用频率。

### 4.5.2 数据来源与生成方式

#### ******* 数据来源
- **主要来源**：content表中该达人的所有内容记录
- **分析字段**：title, description, enhancedTags, hashtags
- **生成方式**：大模型分析生成
- **存储字段**：topThemeHashtags（platforms表新增字段）

#### ******* 大模型分析逻辑
```
输入数据：
- 达人所有内容的标题、描述
- 现有的enhancedTags和hashtags
- 内容发布频率和互动数据

分析任务：
1. 识别最能代表该达人的主题标签
2. 计算各标签的相对使用频率
3. 生成#开头的标准化标签
4. 输出使用频率最高的前5个标签
```

### 4.5.3 字段格式设计

#### ******* 存储格式
```json
// topThemeHashtags字段格式
[
  {"hashtag": "#shorts", "percentage": 3.5},
  {"hashtag": "#alanarmy", "percentage": 3.5},
  {"hashtag": "#entertainment", "percentage": 3.5},
  {"hashtag": "#alan chikin chow", "percentage": 2.8},
  {"hashtag": "#alan chikin chow shorts", "percentage": 2.8}
]
```

#### ******* 标签规范化
```
标签特征：
- 必须以#开头
- 包含个人品牌标签（如：#alanarmy, #nastya）
- 包含内容类型标签（如：#shorts, #entertainment）
- 包含主题标签（如：#kids, #funny, #challenge）
- 可包含个人名称标签（如：#alan chikin chow）
```

### 4.5.4 展示规则
- **显示数量**：固定5个标签
- **展示格式**：#标签名称 + 使用占比
- **排序规则**：按使用频率从高到低排序
- **占比范围**：通常在1-5%之间

### 4.5.5 大模型Prompt设计

#### ******* 主题标签分析Prompt
```
任务：提取网红代表性主题标签

输入数据：
- 内容标题列表：{titles}
- 内容描述列表：{descriptions}
- 现有标签：{existing_tags}
- 网红名称：{influencer_name}

分析要求：
1. 提取最能代表该网红的5个主题标签
2. 标签必须以#开头
3. 计算相对使用频率（1-5%范围）
4. 包含个人品牌、内容类型、主题等标签

输出格式：
[
  {"hashtag": "#标签名称", "percentage": 使用占比},
  ...
]

标签类型示例：
- 个人品牌：#网红名称army, #网红昵称
- 内容类型：#shorts, #entertainment, #tutorial
- 主题标签：#kids, #funny, #challenge, #music
- 平台标签：#foryou, #viral, #trending
```

### 4.5.6 技术实现要点
- **数据更新**：定期重新分析内容生成新的类别和标签
- **缓存机制**：避免频繁调用大模型，设置合适的更新周期
- **数据验证**：确保百分比格式正确，类别标准化
- **异常处理**：处理大模型返回格式异常的情况

## 5. 品牌数据页签

### 5.1 内容推广识别

#### 5.1.1 功能描述
通过大模型分析达人content表中的每条内容，智能识别是否为推广内容，并提取推广的品牌信息。

#### 5.1.2 新增字段设计

**content表新增字段**：
- **isPromoted**：是否推广（Boolean类型）
- **promotedBrand**：推广品牌（String类型，品牌名称或null）

#### 5.1.3 大模型分析逻辑

**输入数据**：
```
- 内容标题：{title}
- 内容描述：{description} 
- 现有标签：{enhancedTags}
- 推广分类：{promotedProductCategory}
- 发布时间：{publishDate}
- 视频封面：{coverImage}
```

**分析任务**：
1. 判断内容是否为推广性质
2. 识别推广的具体品牌
3. 提供判断的置信度
4. 识别推广类型

#### 5.1.4 大模型Prompt设计

```
任务：分析内容是否为推广并识别推广品牌

输入数据：
- 标题：{title}
- 描述：{description}
- 标签：{tags}
- 分类：{category}

分析要求：
1. 判断是否为推广内容（赞助、广告、合作、植入等）
2. 如果是推广，识别具体推广的品牌名称
3. 提供判断置信度（0-1）
4. 识别推广类型

推广识别标准：
- 显性推广：#ad, #sponsored, #partnership, 折扣码, 链接推广
- 隐性推广：产品开箱、评测、使用展示、背景植入
- 品牌合作：官方合作声明、品牌标识露出

输出格式：
{
  "isPromoted": true/false,
  "promotedBrand": "品牌名称" or null,
  "confidence": 0.0-1.0,
  "promotionType": "sponsored|affiliate|gifted|placement|organic",
  "indicators": ["判断依据1", "判断依据2"]
}

品牌识别规则：
- 优先识别知名国际品牌
- 提取标题和描述中的品牌名称
- 识别产品名称对应的品牌
- 排除非商业品牌（如个人名称、地名等）
```

#### 5.1.5 技术实现要点

##### ******* 批量分析策略
```
分析优先级：
1. 新发布内容实时分析
2. 历史内容批量分析（按发布时间倒序）
3. 已有promotedProductCategory字段的内容优先

分析频率：
- 新内容：发布后24小时内完成分析
- 历史内容：每周批量分析1000条
- 重新分析：每月对近3个月内容重新分析
```

##### ******* 数据验证规则
```
验证逻辑：
- 置信度 < 0.6 的结果需要人工审核
- 品牌名称标准化（去除特殊字符、统一大小写）
- 排除无效品牌名称（如：无、未知、其他等）
- 品牌名称长度限制：2-50个字符
```

##### 5.1.5.3 性能优化
- **缓存机制**：已分析内容结果缓存，避免重复分析
- **批量处理**：单次API调用分析多条内容
- **异步处理**：后台异步完成分析，不影响页面展示
- **错误重试**：分析失败自动重试3次

### 5.2 基础数据卡片

#### 5.2.1 品牌广告效果卡片

**显示内容**：
- **互动率数值**：品牌广告内容的平均互动率
- **对比说明**：与整体互动率的对比描述

**数据来源**：
- **品牌广告互动率**：统计推广内容的平均互动率
- **整体互动率**：统计所有内容的平均互动率
- **统计时间范围**：近90天内容数据

**展示格式**：
```
品牌广告效果
2.1%
互动率
该网红的平均互动率为2.7%，其中广告内容的互动率为2.1%
```

#### 5.2.2 发布品牌频次卡片

**显示内容**：
- **月度发布数量**：每月平均品牌广告发布数量
- **占比说明**：品牌广告在总内容中的占比

**数据来源**：
- **月度品牌内容数**：统计推广内容数量并计算月平均值
- **总内容数**：统计所有内容数量
- **统计时间范围**：近90天内容数据

**展示格式**：
```
发布品牌频次
3/每月
每月发布数量
该网红每月发布3条广告内容，占总内容的15%
```

### 5.3 推广视频对比图表

#### 5.3.1 功能描述
柱状图对比推广视频与非推广视频的播放量表现，按时间维度展示数据对比。

#### 5.3.2 图表设计

**图表类型**：分组柱状图
**横坐标**：发布日期（按月或周聚合）
**纵坐标**：平均播放量
**数据系列**：
- **推广视频**：蓝色柱状
- **非推广视频**：灰色柱状

#### 5.3.3 数据来源

**数据统计**：
- **推广视频**：统计推广内容的平均播放量，按月聚合
- **非推广视频**：统计非推广内容的平均播放量，按月聚合
- **统计时间范围**：最近12个月内容数据

#### 5.3.4 展示规则

**时间范围**：最近12个月
**数据聚合**：按月聚合显示
**空值处理**：某月无数据则显示为0
**交互功能**：
- 鼠标悬停显示具体数值
- 支持切换时间粒度（月/周）
- 点击柱状图查看详细内容列表

#### 5.3.5 图表样式

**视觉设计**：
- **推广视频**：蓝色柱状图
- **非推广视频**：灰色柱状图
- **图表标题**：Y轴显示"平均播放量"，X轴显示"发布时间"
- **数据格式**：播放量数字显示千分位分隔符

### 5.4 存储字段设计

#### 5.4.1 platforms表新增字段

| 字段名 | 数据类型 | 描述 | 示例值 | 计算来源 |
|--------|----------|------|--------|----------|
| **brandedAdEngagement** | Float | 品牌广告互动率 | 0.021 | 从contents表中isPromoted=true的记录计算平均互动率 |
| **monthlyBrandedCount** | Integer | 月度品牌内容数量 | 3 | 统计近90天内isPromoted=true的记录数除以3 |
| **brandedContentRatio** | Float | 品牌内容占比 | 0.15 | 品牌内容数量除以总内容数量 |
| **brandedVsOrganicData** | JSON | 推广vs非推广对比数据 | {"promoted": [85000, 92000], "organic": [102000, 98000]} | 按月统计推广和非推广内容的平均播放量 |
- **显示转换**：
  - 4-5分：优秀
  - 3分：良好
  - 1-2分：一般

**数据更新**：
- 更新频率：每日更新
- 计算周期：基于最近90天的内容数据
