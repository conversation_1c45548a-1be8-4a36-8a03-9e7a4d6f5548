# 达人详情页 2.0 需求文档

## 1. 页面顶部 - 达人数据总览卡片

### 1.1 功能描述
页面顶部固定展示达人的数据总览卡片，该卡片不随标签页切换而变化，始终保持在页面最上方。

### 1.2 展示内容

#### 1.2.1 基本信息
- **达人头像**：展示达人的头像图片
- **达人名字**：显示达人的真实姓名或昵称
- **地区和语言**：展示达人所在地区和使用的语言
- **访问链接**：提供达人社交媒体等相关链接

#### 1.2.2 核心数据指标

**粉丝数**
- 数据来源：platforms表中的粉丝数字段
- 展示格式：格式化显示粉丝数量（如：1.2M, 500K等）

**最近发布时间**
- 数据来源：content表
- 计算逻辑：查询content表中最新一条内容的发布时间，计算距今天数
- 展示格式：X天前

**最近推广时间**
- 数据来源：content表
- 计算逻辑：检测content表中推广标注为1的内容，取最新的推广内容发布时间，计算距今天数
- 展示格式：X天前
- 备注：如无推广内容则显示"暂无推广"

**网红综合评分**
- 数据来源：直接读取数据表中的综合评分字段
- 展示格式：数值评分（如：8.5/10）
- 备注：具体评分规则详见下方评分部分

**合作指数**
- 数据来源：根据标签计算得出
- 展示格式：数值评分
- 备注：具体计算规则详见下方合作指数部分

### 1.3 技术实现要点
- 卡片采用固定定位，不随页面滚动
- 数据实时更新，确保信息准确性
- 响应式设计，适配不同屏幕尺寸

## 2. 数据总览页签

### 2.1 基本数据部分

#### 2.1.1 平均观看量
- **数据来源**：最近5期视频的观看量
- **计算逻辑**：计算最近5期视频的观看量均值
- **展示格式**：数值显示（如：1.2M, 500K等）

#### 2.1.2 平均互动量
- **数据来源**：最近5期视频的互动量
- **计算逻辑**：计算最近5期视频的互动量均值
- **展示格式**：数值显示（如：50K, 100K等）

#### 2.1.3 内容数量
- **数据来源**：content表
- **计算逻辑**：统计content表中该网红ID对应的视频总数
- **展示格式**：整数显示（如：158个视频）

#### 2.1.4 观看量/粉丝数
- **数据来源**：平均观看量和粉丝数
- **计算逻辑**：平均观看量 ÷ 粉丝数 × 100%
- **展示格式**：百分比显示（如：15.2%）

#### 2.1.5 预计曝光量
- **数据来源**：平均播放量
- **计算逻辑**：平均播放量 × 90%
- **展示格式**：数值显示（如：1.08M）

### 2.2 增长数据部分

#### 2.2.1 粉丝数
- **数据来源**：platforms表的粉丝数量字段
- **计算逻辑**：直接读取platforms表中该网红的当前粉丝数
- **展示格式**：数值显示（如：1.2M, 500K等）

#### 2.2.2 观看量
- **数据来源**：content表中该网红所有视频的观看量
- **计算逻辑**：累加content表中该网红ID对应的所有视频观看量
- **展示格式**：数值显示（如：10.5M, 2.3B等）

#### 2.2.3 发布量
- **数据来源**：content表的内容发布日期
- **计算逻辑**：累加统计到指定日期该网红的作品总数
  - 按发布日期排序，计算每日累计发布量
  - 例如：1号发布1个视频累计为1，2号发布1个视频累计为2，3号发布1个视频累计为3
- **展示格式**：整数显示（如：158个作品）

#### 2.2.4 增长趋势曲线
- **数据来源**：基于历史数据的增长率计算
- **计算逻辑**：
  - 三条曲线分别对应粉丝数、观看量、发布量的增长趋势
  - 以第一天数据为基准，计算后续增长率
  - 使用随机斜率进行数据拟合，斜率范围：0.8-0.9之间
- **展示格式**：折线图形式展示增长趋势
- **技术实现**：使用假数据拟合算法生成平滑增长曲线

### 2.3 频道质量评分部分

#### 2.3.1 评分规则体系
**评分制度**：5分满分制，只能整数分
**排名分布评分标准**：
- 前10%：5分
- 前30%：4分
- 前50%：3分
- 前80%：2分
- 其他：1分

#### 2.3.2 粉丝增长评分（按排名评分）
- **数据来源**：platforms表的粉丝数
- **计算逻辑**：
  - 由于是静态demo，生成假数据：随机生成0.5%-5%的月增长率
  - 计算公式：月增长率 = 随机数(0.5%, 5%)
- **评分规则**：按排名分布评分标准评分

#### 2.3.3 创作频率评分（按排名评分）
- **数据来源**：content表的发布时间
- **计算逻辑**：统计最近30天发布内容数量
- **评分规则**：按排名分布评分标准评分

#### 2.3.4 互动率评分（按排名评分）
- **数据来源**：content表的互动量和观看量
- **计算逻辑**：(近期点赞数+评论数+分享数)/近期观看量总和
- **评分规则**：按排名分布评分标准评分

#### 2.3.5 频道质量评分（大模型评分）
- **数据来源**：content表的内容数据
- **计算逻辑**：大模型分析内容质量，直接给出1-5分
- **大模型评分要求**：
  - 分析内容创意度、制作质量、信息价值
  - 考虑视频时长合理性、内容完整度
  - 直接输出1-5分整数评分

#### 2.3.6 粉丝可信度评分（大模型评分）
- **数据来源**：content表的评论数据
- **计算逻辑**：大模型分析评论质量，直接给出1-5分
- **大模型评分要求**：
  - 分析评论的真实性、多样性、互动质量
  - 识别机器人评论、重复评论等异常行为
  - 直接输出1-5分整数评分

#### 2.3.7 综合评分计算
- **计算公式**：(粉丝增长 + 创作频率 + 互动率 + 频道质量 + 粉丝可信度) ÷ 5
- **结果处理**：四舍五入到整数，最终得分1-5分
- **数据存储**：各维度评分及综合评分存储到platforms表中
- **展示格式**：五角星评分和雷达图形式展示各维度评分

**字段设计**：
- **粉丝增长评分**：follower_growth_score (INT 1-5)
- **创作频率评分**：content_frequency_score (INT 1-5)
- **互动率评分**：engagement_rate_score (INT 1-5)
- **频道质量评分**：channel_quality_score (INT 1-5)
- **粉丝可信度评分**：follower_credibility_score (INT 1-5)
- **综合评分**：total_quality_score (INT 1-5)
- **存储位置**：platforms表
- **更新机制**：当相关数据变化时重新计算并更新各字段
- **读取说明**：其他页面可直接读取这些字段展示达人评分

### 2.4 合作倾向评分部分

#### 2.4.1 内容更新频率（时效性）
- **数据来源**：content表的最新发布时间
- **计算逻辑**：查询content表中该达人最后发布内容的日期，计算距离当前时间的天数
- **评分规则**：
  - **0-3天有内容更新**：3分
  - **4-14天有内容更新**：2分  
  - **15-30天有内容更新**：1分
  - **超过30天未更新**：0分

#### 2.4.2 内容产量（活跃度）
- **数据来源**：content表近30天的发布数量
- **评分规则**：
  - **近30天内更新10+内容**：3分
  - **近30天内更新7-9内容**：2分
  - **近30天内更新3-6内容**：1分
  - **近30天内更新少于3内容**：0分

#### 2.4.3 联系便利性（沟通成本）
- **数据来源**：达人的联系方式信息和历史沟通记录
- **评分规则**：
  - **有联系方式 + 半年内回复过邮件**：3分
  - **仅有联系方式**：2分
  - **无任何联系方式**：0分

#### 2.4.4 合作倾向总分计算
- **计算公式**：内容更新频率分数 + 内容产量分数 + 联系便利性分数
- **分数范围**：0-9分
- **数据存储**：计算完成后存储到platforms表的cooperation_score字段中
- **展示格式**：数值显示 + 评级标识

**评级标准**：
- **8-9分**：优秀合作倾向
- **6-7分**：良好合作倾向  
- **4-5分**：一般合作倾向
- **2-3分**：较低合作倾向
- **0-1分**：不推荐合作

**字段设计**：
- **字段名**：cooperation_score
- **数据类型**：INT (0-9)
- **存储位置**：platforms表
- **更新机制**：当达人内容更新或联系方式变化时重新计算并更新
- **读取说明**：其他页面可直接读取此字段展示合作倾向评分

#### 2.4.5 合作行为标签详细展示
- **数据来源**：platforms表的collaborationLabels字段
- **标签生成**：根据合作倾向评分的3个维度自动生成对应标签
- **标签类型**：
  - **内容更新频率标签**：
    - "0-3天有内容更新"
    - "4-14天有内容更新"
    - "15-30天有内容更新"
    - "超过30天未更新"
  - **内容产量标签**：
    - "近30天内更新10+内容"
    - "近30天内更新7-9内容"
    - "近30天内更新3-6内容"
    - "近30天内更新少于3内容"
  - **联系便利性标签**：
    - "有联系方式"
    - "半年内回复过邮件"
    - "无联系方式"
- **展示位置**：在合作倾向评分下方以标签形式展示
- **颜色编码**：
  - 绿色：积极正面标签（高分标签）
  - 灰色：消极标签（低分标签）
- **标签排序**：按照标签对合作倾向的影响程度排序
- **标签更新**：与合作倾向评分同步更新

### 2.5 合作价格部分

#### 2.5.1 CPM价格获取
- **数据来源**：CPM查询表
- **查询维度**：
  - **行业**：从platforms表的行业分类字段读取
  - **粉丝数范围**：根据platforms表的subscribers字段确定范围区间
  - **地区**：从platforms表的地区字段读取
  - **平台**：从platforms表的platform字段读取
- **计算逻辑**：根据这4个维度在CPM查询表中匹配获取对应的CPM价格范围
- **存储字段**：将CPM价格范围的中位数存储到platforms表的estimatedCPM字段

#### 2.5.2 植入视频价格范围计算
- **数据来源**：
  - CPM价格范围：从CPM查询表获取的价格范围（如：$20-$40）
  - 粉丝数：platforms表的subscribers字段
- **计算逻辑**：
  - 最低报价 = CPM最低价 × 粉丝数 ÷ 1000
  - 最高报价 = CPM最高价 × 粉丝数 ÷ 1000
  - 价格范围 = [最低报价, 最高报价]
- **存储字段**：将计算结果存储到platforms表的estimatedVideoPrice字段
- **展示格式**：价格区间形式（如：$500-$1500）

#### 2.5.3 价格更新机制
- **更新频率**：每周更新一次
- **更新流程**：
  1. 从CPM查询表获取最新CPM价格范围
  2. 计算CPM中位数并存储到estimatedCPM字段
  3. 根据CPM价格范围重新计算植入视频价格范围
  4. 更新platforms表中的estimatedVideoPrice字段

#### 2.5.4 价格展示规范
- **货币符号**：统一使用美元符号"$"
- **数值简写**：超过1000时使用"k"简写（如：$1.5k）
- **区间格式**：始终显示最小值-最大值

### 2.6 排名展示部分

#### 2.6.1 粉丝数世界排名
- **数据来源**：platforms表的globalRank字段
- **计算逻辑**：根据全球达人粉丝数排名计算百分比位置
- **展示格式**：百分比形式（如：粉丝数世界排名：1%）

#### 2.6.2 所在国家排名
- **数据来源**：platforms表的countryRank字段
- **计算逻辑**：根据达人所在国家的粉丝数排名计算百分比位置
- **展示格式**：百分比形式（如：所在国家排名：5%）
