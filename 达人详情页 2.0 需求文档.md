# 达人详情页 2.0 需求文档

## 1. 页面顶部 - 达人数据总览卡片

### 1.1 功能描述
页面顶部固定展示达人的数据总览卡片，该卡片不随标签页切换而变化，始终保持在页面最上方。

### 1.2 展示内容

#### 1.2.1 基本信息
- **达人头像**：展示达人的头像图片
- **达人名字**：显示达人的真实姓名或昵称
- **地区和语言**：展示达人所在地区和使用的语言
- **访问链接**：提供达人社交媒体等相关链接

#### 1.2.2 核心数据指标

**粉丝数**
- 数据来源：platforms表中的粉丝数字段
- 展示格式：格式化显示粉丝数量（如：1.2M, 500K等）

**最近发布时间**
- 数据来源：content表
- 计算逻辑：查询content表中最新一条内容的发布时间，计算距今天数
- 展示格式：X天前

**最近推广时间**
- 数据来源：content表
- 计算逻辑：检测content表中推广标注为1的内容，取最新的推广内容发布时间，计算距今天数
- 展示格式：X天前
- 备注：如无推广内容则显示"暂无推广"

**网红综合评分**
- 数据来源：直接读取数据表中的综合评分字段
- 展示格式：数值评分（如：8.5/10）
- 备注：具体评分规则详见下方评分部分

**合作指数**
- 数据来源：根据标签计算得出
- 展示格式：数值评分
- 备注：具体计算规则详见下方合作指数部分

### 1.3 技术实现要点
- 卡片采用固定定位，不随页面滚动
- 数据实时更新，确保信息准确性
- 响应式设计，适配不同屏幕尺寸

## 2. 数据总览页签

### 2.1 基本数据部分

#### 2.1.1 平均观看量
- **数据来源**：最近5期视频的观看量
- **计算逻辑**：计算最近5期视频的观看量均值
- **展示格式**：数值显示（如：1.2M, 500K等）

#### 2.1.2 平均互动量
- **数据来源**：最近5期视频的互动量
- **计算逻辑**：计算最近5期视频的互动量均值
- **展示格式**：数值显示（如：50K, 100K等）

#### 2.1.3 内容数量
- **数据来源**：content表
- **计算逻辑**：统计content表中该网红ID对应的视频总数
- **展示格式**：整数显示（如：158个视频）

#### 2.1.4 观看量/粉丝数
- **数据来源**：平均观看量和粉丝数
- **计算逻辑**：平均观看量 ÷ 粉丝数 × 100%
- **展示格式**：百分比显示（如：15.2%）

#### 2.1.5 预计曝光量
- **数据来源**：平均播放量
- **计算逻辑**：平均播放量 × 90%
- **展示格式**：数值显示（如：1.08M）

### 2.2 增长数据部分

#### 2.2.1 粉丝数
- **数据来源**：platforms表的粉丝数量字段
- **计算逻辑**：直接读取platforms表中该网红的当前粉丝数
- **展示格式**：数值显示（如：1.2M, 500K等）

#### 2.2.2 观看量
- **数据来源**：content表中该网红所有视频的观看量
- **计算逻辑**：累加content表中该网红ID对应的所有视频观看量
- **展示格式**：数值显示（如：10.5M, 2.3B等）

#### 2.2.3 发布量
- **数据来源**：content表的内容发布日期
- **计算逻辑**：累加统计到指定日期该网红的作品总数
  - 按发布日期排序，计算每日累计发布量
  - 例如：1号发布1个视频累计为1，2号发布1个视频累计为2，3号发布1个视频累计为3
- **展示格式**：整数显示（如：158个作品）

#### 2.2.4 增长趋势曲线
- **数据来源**：基于历史数据的增长率计算
- **计算逻辑**：
  - 三条曲线分别对应粉丝数、观看量、发布量的增长趋势
  - 以第一天数据为基准，计算后续增长率
  - 使用随机斜率进行数据拟合，斜率范围：0.8-0.9之间
- **展示格式**：折线图形式展示增长趋势
- **技术实现**：使用假数据拟合算法生成平滑增长曲线

### 2.3 频道质量评分部分

#### 2.3.1 评分规则体系
**评分制度**：5分满分制，只能整数分
**排名分布评分标准**：
- 前10%：5分
- 前30%：4分
- 前50%：3分
- 前80%：2分
- 其他：1分

#### 2.3.2 粉丝增长评分（按排名评分）
- **数据来源**：platforms表的粉丝数
- **计算逻辑**：
  - 由于是静态demo，生成假数据：随机生成0.5%-5%的月增长率
  - 计算公式：月增长率 = 随机数(0.5%, 5%)
- **评分规则**：按排名分布评分标准评分

#### 2.3.3 创作频率评分（按排名评分）
- **数据来源**：content表的发布时间
- **计算逻辑**：统计最近30天发布内容数量
- **评分规则**：按排名分布评分标准评分

#### 2.3.4 互动率评分（按排名评分）
- **数据来源**：content表的互动量和观看量
- **计算逻辑**：(近期点赞数+评论数+分享数)/近期观看量总和
- **评分规则**：按排名分布评分标准评分

#### 2.3.5 频道质量评分（大模型评分）
- **数据来源**：content表的内容数据
- **计算逻辑**：大模型分析内容质量，直接给出1-5分
- **大模型评分要求**：
  - 分析内容创意度、制作质量、信息价值
  - 考虑视频时长合理性、内容完整度
  - 直接输出1-5分整数评分

#### 2.3.6 粉丝可信度评分（大模型评分）
- **数据来源**：content表的评论数据
- **计算逻辑**：大模型分析评论质量，直接给出1-5分
- **大模型评分要求**：
  - 分析评论的真实性、多样性、互动质量
  - 识别机器人评论、重复评论等异常行为
  - 直接输出1-5分整数评分

#### 2.3.7 综合评分计算
- **计算公式**：(粉丝增长 + 创作频率 + 互动率 + 频道质量 + 粉丝可信度) ÷ 5
- **结果处理**：四舍五入到整数，最终得分1-5分
- **数据存储**：各维度评分及综合评分存储到platforms表中
- **展示格式**：五角星评分和雷达图形式展示各维度评分

**评级标准**：
- **4.0-5.0分**：优秀 - 综合表现卓越，合作价值极高
- **3.5-3.9分**：良好 - 表现出色，具有较高合作潜力
- **3.0-3.4分**：中等 - 表现尚可，有一定合作价值
- **2.0-2.9分**：一般 - 表现平平，需谨慎考虑合作
- **1.0-1.9分**：较差 - 表现不佳，不建议合作

**卡片展示格式**：
- **分数显示**：X.X/5.0 格式（如：4.2/5.0）
- **文本描述**：换行显示对应评级文本
- **示例**：
  ```
  4.2/5.0
  优秀 - 综合表现卓越，合作价值极高
  ```

**颜色编码**：
- **优秀（4.0-5.0）**：深绿色
- **良好（3.5-3.9）**：绿色
- **中等（3.0-3.4）**：黄色
- **一般（2.0-2.9）**：橙色
- **较差（1.0-1.9）**：红色

**字段设计**：
- **粉丝增长评分**：follower_growth_score (INT 1-5)
- **创作频率评分**：content_frequency_score (INT 1-5)
- **互动率评分**：engagement_rate_score (INT 1-5)
- **频道质量评分**：channel_quality_score (INT 1-5)
- **粉丝可信度评分**：follower_credibility_score (INT 1-5)
- **综合评分**：total_quality_score (INT 1-5)
- **存储位置**：platforms表
- **更新机制**：当相关数据变化时重新计算并更新各字段
- **读取说明**：其他页面可直接读取这些字段展示达人评分

### 2.4 合作倾向评分部分

#### 2.4.1 内容更新频率（时效性）
- **数据来源**：content表的最新发布时间
- **计算逻辑**：查询content表中该达人最后发布内容的日期，计算距离当前时间的天数
- **评分规则**：
  - **0-3天有内容更新**：3分
  - **4-14天有内容更新**：2分  
  - **15-30天有内容更新**：1分
  - **超过30天未更新**：0分

#### 2.4.2 内容产量（活跃度）
- **数据来源**：content表近30天的发布数量
- **评分规则**：
  - **近30天内更新15+内容**：4分
  - **近30天内更新10-14内容**：3分
  - **近30天内更新5-9内容**：2分
  - **近30天内更新1-4内容**：1分
  - **近30天内无内容更新**：0分

#### 2.4.3 联系便利性（沟通成本）
- **数据来源**：达人的联系方式信息和历史沟通记录
- **评分规则**：
  - **有联系方式 + 半年内回复过邮件**：3分
  - **仅有联系方式**：2分
  - **无任何联系方式**：0分

#### 2.4.4 合作倾向总分计算
- **计算公式**：内容更新频率分数 + 内容产量分数 + 联系便利性分数
- **分数范围**：0-10分（原始分数）
- **数据存储**：计算完成后存储到platforms表的cooperation_score字段中
- **展示格式**：数值显示 + 评级标识

**评级标准**：
- **9-10分**：良好 - 较为明显的合作潜力与意向
- **7-8分**：及格 - 通常活跃，有一定合作意向
- **5-6分**：一般 - 合作意向不明显
- **3-4分**：较低 - 合作概率相对较低
- **0-2分**：差 - 合作概率相对较低

**卡片展示格式**：
- **分数显示**：X/10 格式（如：2/10）
- **文本描述**：换行显示对应评级文本
- **示例**：
  ```
  2/10
  差 - 合作概率相对较低
  ```

**字段设计**：
- **字段名**：cooperation_score
- **数据类型**：INT (0-10)
- **存储位置**：platforms表
- **更新机制**：当达人内容更新或联系方式变化时重新计算并更新
- **读取说明**：其他页面可直接读取此字段展示合作倾向评分

#### 2.4.5 合作行为标签详细展示
- **数据来源**：platforms表的collaborationLabels字段
- **标签生成**：根据合作倾向评分的3个维度自动生成对应标签
- **标签类型**：
  - **内容更新频率标签**：
    - "0-3天有内容更新"
    - "4-14天有内容更新"
    - "15-30天有内容更新"
    - "超过30天未更新"
  - **内容产量标签**：
    - "近30天内更新15+内容"
    - "近30天内更新10-14内容"
    - "近30天内更新5-9内容"
    - "近30天内更新1-4内容"
    - "近30天内无内容更新"
  - **联系便利性标签**：
    - "有联系方式"
    - "半年内回复过邮件"
    - "无联系方式"
- **展示位置**：在合作倾向评分下方以标签形式展示
- **颜色编码**：
  - 绿色：积极正面标签（高分标签）
  - 灰色：消极标签（低分标签）
- **标签排序**：按照标签对合作倾向的影响程度排序
- **标签更新**：与合作倾向评分同步更新

### 2.5 合作价格部分

#### 2.5.1 CPM价格获取
- **数据来源**：CPM查询表
- **查询维度**：
  - **行业**：从platforms表的行业分类字段读取
  - **粉丝数范围**：根据platforms表的subscribers字段确定范围区间
  - **地区**：从platforms表的地区字段读取
  - **平台**：从platforms表的platform字段读取
- **计算逻辑**：根据这4个维度在CPM查询表中匹配获取对应的CPM价格范围
- **存储字段**：将CPM价格范围的中位数存储到platforms表的estimatedCPM字段

#### 2.5.2 植入视频价格范围计算
- **数据来源**：
  - CPM价格范围：从CPM查询表获取的价格范围（如：$20-$40）
  - 粉丝数：platforms表的subscribers字段
- **计算逻辑**：
  - 最低报价 = CPM最低价 × 粉丝数 ÷ 1000
  - 最高报价 = CPM最高价 × 粉丝数 ÷ 1000
  - 价格范围 = [最低报价, 最高报价]
- **存储字段**：将计算结果存储到platforms表的estimatedVideoPrice字段
- **展示格式**：价格区间形式（如：$500-$1500）

#### 2.5.3 价格更新机制
- **更新频率**：每周更新一次
- **更新流程**：
  1. 从CPM查询表获取最新CPM价格范围
  2. 计算CPM中位数并存储到estimatedCPM字段
  3. 根据CPM价格范围重新计算植入视频价格范围
  4. 更新platforms表中的estimatedVideoPrice字段

#### 2.5.4 价格展示规范
- **货币符号**：统一使用美元符号"$"
- **数值简写**：超过1000时使用"k"简写（如：$1.5k）
- **区间格式**：始终显示最小值-最大值

### 2.6 排名展示部分

#### 2.6.1 粉丝数世界排名
- **数据来源**：platforms表的globalRank字段
- **计算逻辑**：根据全球达人粉丝数排名计算百分比位置
- **展示格式**：百分比形式（如：粉丝数世界排名：1%）

#### 2.6.2 所在国家排名
- **数据来源**：platforms表的countryRank字段
- **计算逻辑**：根据达人所在国家的粉丝数排名计算百分比位置
- **展示格式**：百分比形式（如：所在国家排名：5%）

## 3. 受众数据页签

### 3.1 基本数据部分

#### 3.1.1 粉丝可信度评分
- **数据来源**：大模型分析粉丝行为数据
- **计算逻辑**：大模型分析粉丝增长模式、互动质量、评论真实性等维度
- **评分要求**：
  - 分析粉丝增长是否自然
  - 识别机器人粉丝和虚假互动
  - 评估评论质量和真实性
  - 检测异常增长模式
  - 直接输出1-5分整数评分
- **存储字段**：platforms表的follower_credibility_score字段
- **字段格式**：INT(1-5)
- **展示格式**：1-5分评分

#### 3.1.2 受众区域分布
- **数据来源**：大模型分析受众地理位置数据
- **计算逻辑**：大模型分析受众的地理分布情况
- **分析要求**：
  - 分析受众主要来源地区
  - 计算各地区受众百分比
  - 识别核心受众市场
  - 评估地区分布的合理性
- **存储字段**：platforms表的geoDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "北美": 45.2,
    "欧洲": 28.5,
    "亚洲": 18.3,
    "南美": 5.8,
    "其他": 2.2
  }
  ```
- **展示格式**：地图或饼图形式展示各地区百分比

#### 3.1.3 最多受众性别
- **数据来源**：大模型分析受众性别数据
- **计算逻辑**：大模型分析受众的性别分布情况
- **分析要求**：
  - 分析受众性别比例
  - 识别主要受众性别
  - 评估性别分布特征
- **存储字段**：platforms表的genderDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "男性": 42.0,
    "女性": 58.0
  }
  ```
- **展示格式**：文本 + 百分比显示

#### 3.1.4 最多受众年龄
- **数据来源**：大模型分析受众年龄数据
- **计算逻辑**：大模型分析受众的年龄分布情况
- **分析要求**：
  - 分析受众年龄分布
  - 识别主要受众年龄段
  - 评估年龄分布特征
- **存储字段**：platforms表的ageDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "13-17": 8.2,
    "18-24": 42.3,
    "25-34": 28.5,
    "35-44": 15.7,
    "45-54": 3.8,
    "55+": 1.5
  }
  ```
- **展示格式**：文本 + 百分比显示

#### 3.1.5 基本数据卡片展示
- **卡片布局**：4个卡片横向排列（audience-overview-grid）
- **卡片结构**：
  - 图标区域：使用FontAwesome图标
  - 数据区域：显示主要数值和百分比
  - 状态区域：显示评级或描述
- **卡片样式**：
  - 粉丝可信度：星形图标，显示评分和等级
  - 受众区域：地图图标，显示主要地区
  - 受众性别：性别图标，显示主要性别和百分比
  - 受众年龄：生日蛋糕图标，显示主要年龄段和百分比

#### 3.1.6 受众语言分布
- **数据来源**：大模型分析受众语言数据
- **计算逻辑**：大模型分析受众的语言分布情况
- **分析要求**：
  - 分析受众主要使用语言
  - 计算各语言使用者百分比
  - 识别核心语言市场
- **存储字段**：platforms表的languageDistribution字段
- **字段格式**：JSON
- **数据格式**：
  ```json
  {
    "英语": 84.5,
    "西班牙语": 7.3,
    "葡萄牙语": 6.4,
    "其他语言": 1.8
  }
  ```
- **展示格式**：在受众特征部分显示

### 3.2 受众特征部分

#### 3.2.1 受众区域分布展示
- **展示方式**：横向条形图 + 详细列表
- **排序规则**：按百分比从高到低排序
- **数据来源**：platforms表的geoDistribution字段
- **展示内容**：地区名称、百分比、可视化条形图

#### 3.2.2 受众语言分布展示
- **展示方式**：横向条形图 + 详细列表
- **排序规则**：按百分比从高到低排序
- **数据来源**：platforms表的languageDistribution字段
- **展示内容**：语言名称、百分比、可视化条形图

#### 3.2.3 性别分布展示
- **展示方式**：圆环图（甜甜圈图）
- **数据来源**：platforms表的genderDistribution字段
- **展示内容**：男性、女性两个分类的百分比
- **图表特点**：二分性别展示，中心显示总计100%

#### 3.2.4 年龄分布展示
- **展示方式**：条形图
- **数据来源**：platforms表的ageDistribution字段
- **年龄分段**：13-17, 18-24, 25-34, 35-44, 45-54, 55-64, 65+
- **展示内容**：各年龄段的百分比分布

### 3.3 营销分析部分

#### 3.3.1 受众反馈指标（2个百分比指标）

**正向反馈百分比**
- **数据来源**：大模型分析评论内容的情感倾向
- **计算逻辑**：统计正面评论占总评论数的百分比
- **存储字段**：platforms表的positiveFeedbackRatio字段
- **字段格式**：Float (0-100)
- **展示格式**：百分比显示（如：61%）

**内容感兴趣百分比**
- **数据来源**：大模型分析推广内容的受众反馈
- **计算逻辑**：统计对推广内容感兴趣的评论占总评论数的百分比
- **存储字段**：platforms表的promoInterestRatio字段
- **字段格式**：Float (0-100)
- **展示格式**：百分比显示（如：60%）

#### 3.3.2 消费影响力指标（2个评分指标）

**推广吸引度**
- **数据来源**：大模型分析推广内容的吸引力
- **计算逻辑**：大模型综合分析推广内容的互动表现、评论质量等
- **评分范围**：0-5分，每0.5分一个档位
- **存储字段**：platforms表的promoAttractionScore字段
- **字段格式**：Integer (0-10，实际显示时除以2)
- **展示格式**：小数显示（如：3.5分）

**推广专业度**
- **数据来源**：大模型分析推广内容的专业性
- **计算逻辑**：大模型评估推广内容与达人专业领域的匹配度
- **评分范围**：0-5分，每0.5分一个档位
- **存储字段**：platforms表的promoProfessionalScore字段
- **字段格式**：Integer (0-10，实际显示时除以2)
- **展示格式**：小数显示（如：1.5分）

#### 3.3.3 营销分析文本展示规范

**正向反馈百分比文本模板**
- **好（>50%）**："{达人名称}的内容广受欢迎，有高达{百分比}的观众表达了积极的评价，具有极佳的受众吸引力和满意度。"
- **中（30-50%）**："{达人名称}的内容获得了较为积极的反馈，{百分比}的观众给出了正面评价，整体表现良好。"
- **差（<30%）**："{达人名称}的内容反馈一般，仅有{百分比}的观众给出了正面评价，需要进一步提升内容质量。"

**内容感兴趣百分比文本模板**
- **好（>50%）**："{达人名称}对推广产品的呈现方式获得了观众的一致好评，正面评论的比例高达{百分比}，这个频道的品牌推广效果极为出色，受众对此表示极高的认可。"
- **中（30-50%）**："{达人名称}的推广内容获得了不错的反响，{百分比}的观众对推广产品表示感兴趣，推广效果较为理想。"
- **差（<30%）**："{达人名称}的推广内容关注度不高，仅有{百分比}的观众对推广产品表示感兴趣，推广效果有待提升。"

**推广吸引度文本模板**
- **好（>3.5分）**："{达人名称}的推广内容具有出色的吸引力，吸引度指数达到{分数}，推广内容与自然内容的互动表现基本一致，合作价值很高。"
- **中（2.0-3.5分）**："{达人名称}的推广内容吸引度中等，吸引度指数为{分数}，推广效果尚可，有一定的合作潜力。"
- **差（<2.0分）**："{达人名称}的推广内容似乎并未达到预期效果，吸引度指数仅为{分数}，相比自然内容，互动量水平并不符合预期。如果合作，需要投入更多的精力到内容创意上。"

**推广专业度文本模板**
- **好（>3.5分）**："{达人名称}在推广领域表现专业，专业指数达到{分数}，推广内容与其专业领域高度匹配，合作效果值得期待。"
- **中（2.0-3.5分）**："{达人名称}的推广专业度尚可，专业指数为{分数}，推广内容基本符合其专业领域，合作效果一般。"
- **差（<2.0分）**："{达人名称}的推广专业度有待提高，专业指数仅为{分数}。其推广内容可能并非其专业领域，创作主题不够专一，需调整推广策略以提升其专业性。"

### 3.4 内容与兴趣部分

#### 3.4.1 数据分析和存储

**数据来源**：大模型分析content表中该达人的所有内容
**计算逻辑**：
- 大模型分析视频内容自动分类到预设兴趣类别
- 统计各类别视频数量和占比
- 选择占比最高的前5个类别展示
- 其余类别合并为"其他"

**存储字段**：platforms表的contentDistribution字段
**字段格式**：JSON

#### 3.4.2 页面布局规范

**整体布局**：
- 左侧：圆环图区域（占宽度40%）
- 右侧：表格区域（占宽度60%）
- 两个区域并排展示，响应式适配

#### 3.4.3 左侧圆环图展示规则

**圆环图规格**：
- 图表类型：甜甜圈图（圆环图）
- 显示数据：前5个类别 + 其他类别（如有）
- 圆环粗细：中等粗细，中心留白50%
- 颜色方案：使用6种不同颜色区分类别

**图表标注**：
- 中心文字：显示"内容分布"和"Top 5 分类"
- 鼠标悬停：显示类别名称和百分比
- 图例：不显示独立图例

#### 3.4.4 右侧表格展示规则

**表格结构**：
- 表头：兴趣点 | 描述 | 百分比
- 列宽比例：1:3:1
- 固定显示5行数据

**表格内容**：
- 兴趣点列：显示类别名称
- 描述列：显示完整的描述文本
- 百分比列：显示百分比数值 + 进度条

**进度条规则**：
- 每行百分比下方显示横向进度条
- 进度条长度对应百分比数值
- 使用渐变色彩填充

#### 3.4.5 描述文本生成规则

**展示规则**：始终显示5行文本，每行对应一个类别

**固定文本模板**：
```
{网红名}的{类别1}内容占其整个账户的{百分比1}%，展现了其在该领域的专业程度和受众喜爱。
{网红名}在{类别2}领域的内容占比为{百分比2}%，体现了其多元化的内容创作能力。
{网红名}的{类别3}相关视频占总内容的{百分比3}%，吸引了大量相关爱好者的关注。
{网红名}制作的{类别4}内容占比达到{百分比4}%，显示了其在该领域的活跃度。
{网红名}的{类别5}视频内容占其账户的{百分比5}%，丰富了频道的内容多样性。
```

**实际展示示例**：
```
MrBeast的汽车内容占其整个账户的32%，展现了其在该领域的专业程度和受众喜爱。
MrBeast在手机游戏领域的内容占比为18%，体现了其多元化的内容创作能力。
MrBeast的宠物&动物相关视频占总内容的17%，吸引了大量相关爱好者的关注。
MrBeast制作的表演&喜剧内容占比达到17%，显示了其在该领域的活跃度。
MrBeast的vlog视频内容占其账户的17%，丰富了频道的内容多样性。
```

**数据更新**：每月更新一次，基于最近90天的视频内容统计

### 3.5 粉丝可信度部分

#### 3.5.1 数据分析和计算

**数据来源**：大模型分析content表中该达人的评论数据和粉丝行为数据
**计算逻辑**：
- 大模型分析粉丝互动行为、评论质量、账户活跃度等
- 将粉丝分类为4种类型：普通粉、可疑粉、僵尸粉、网红粉
- 计算各类型粉丝的占比
- 计算真实受众比例（普通粉 + 网红粉）

**存储字段**：
- platforms表的followerTypeDistribution字段：存储四种粉丝类型占比
- platforms表的realFollowerRatio字段：存储真实受众比例

**字段格式**：
- followerTypeDistribution：JSON格式
- realFollowerRatio：Float格式

**数据格式**：
```json
{
  "普通粉": 76.8,
  "可疑粉": 14.3,
  "僵尸粉": 6.2,
  "网红粉": 2.7
}
```

#### 3.5.2 页面布局规范

**整体布局**：
- 标题区域：显示"粉丝可信度"标题和图标
- 卡片区域：两个并排的卡片
  - 左侧：受众类型卡片
  - 右侧：真实受众卡片

#### 3.5.3 受众类型卡片展示规则

**卡片结构**：
- 卡片标题：显示"受众类型"
- 图表区域：圆形饼图
- 图例区域：四种粉丝类型的详细列表
- 说明文本区域

**圆形饼图规格**：
- 图表类型：pie图（完整圆形）
- 显示数据：四种粉丝类型占比
- 颜色方案：
  - 普通粉：蓝色（#4285F4）
  - 可疑粉：橙色（#FF9800）
  - 僵尸粉：绿色（#4CAF50）
  - 网红粉：紫色（#9C27B0）

**图例列表规格**：
- 每行显示：颜色条 + 类型名称 + 百分比
- 颜色条：水平条形图，长度对应占比
- 排序：按占比从高到低排列

**说明文本**：
"受众类型中，真实用户为两种属性，普通用户粉丝和有影响力的网红关注者，虚假受众为有可疑点赞评论行为的帐户和大量关注取关账户"

#### 3.5.4 真实受众卡片展示规则

**卡片结构**：
- 卡片标题：显示"真实受众"
- 图表区域：圆环图
- 描述区域：数据说明
- 说明文本区域

**圆环图规格**：
- 图表类型：doughnut图（圆环图）
- 圆环粗细：中心留白70%
- 显示数据：真实受众比例 vs 非真实受众比例
- 颜色方案：真实受众为蓝色，非真实受众为灰色

**中心数据显示**：
- 中心显示真实受众百分比（如：79.5%）
- 下方显示"真实"标签

**描述文本格式**：
"网红@{达人名称} 的真实受众为{真实比例}%"

**说明文本**：
"网红频道的真实受众关注者"
