# 达人详情页增强需求文档

## 1. 引言

### 1.1 项目概述

基于对标NOX Influencer平台的达人详情页功能分析，本文档定义了达人详情页的全面增强需求。旨在提供丰富的达人数据展示、智能化的品牌分析、多平台账号关联以及完整的数据可视化功能，以提升用户对达人的全面了解和商业价值评估能力。

### 1.2 设计目标

- **数据丰富性：** 提供四大核心模块的完整数据展示（数据总览、内容数据、受众数据、品牌数据）
- **智能化分析：** 通过AI技术自动分析达人内容中的品牌提及情况
- **多平台整合：** 支持跨平台达人账号的关联和统一展示
- **可视化展示：** 通过图表和数据可视化提升用户体验
- **商业价值评估：** 提供CPM计算、合作倾向等商业化数据支持

### 1.3 技术架构

- **前端展示：** 基于现有Web应用框架的响应式设计
- **数据获取：** 通过API接口获取达人多维度数据
- **AI分析：** 集成LLM大模型进行品牌提及智能识别
- **数据存储：** 基于现有数据库结构的扩展和优化

---

## 2. 功能需求

### 2.1 数据总览模块

#### 2.1.1 达人基本信息展示
- **头像与身份信息：**
  - 达人头像、用户名、平台标识
  - 国家/地区标识（国旗图标）
  - 认证状态标识（如适用）
  - 主要内容分类标签

- **核心数据指标：**
  - **粉丝数量：** 显示当前粉丝总数，支持千万单位简化显示
  - **最近发布时间：** 距离最后一次内容发布的时间
  - **最近推广时间：** 距离最后一次商业推广内容的时间
  - **Nox评分：** 综合质量评分（1-5分制）
  - **合作倾向：** 合作可能性评分（1-10分制）

#### 2.1.2 基础数据展示
- **内容表现数据（基于近30个内容）：**
  - **平均观看量：** 数值显示+变化趋势百分比
  - **平均互动量：** 点赞、评论、分享的综合数据
  - **内容数量：** 统计周期内的内容发布数量
  - **观看量/粉丝数比例：** 反映内容传播效果

- **数据等级标识：**
  - 通过颜色编码显示各项数据的表现等级（优秀/良好/中等/较差）
  - 每个数据项旁显示对应的评级文字和颜色标识

#### 2.1.3 增长数据可视化
- **统计数据总览：**
  - 粉丝数、累计点赞数、视频总数的卡片式展示
  - 支持不同时间周期的数据对比（30天、90天、1年）

- **增长趋势图表：**
  - 粉丝增长趋势折线图
  - 内容发布量趋势图
  - 互动量变化趋势图
  - 图表支持时间范围选择和数据点详情查看

#### 2.1.4 频道质量评估
- **Nox评分雷达图：**
  - 六个维度的雷达图展示：粉丝增长、创作频率、频道质量、互动率、粉丝可信度、合作率
  - 每个维度显示具体分数和等级标识
  - 支持鼠标悬停查看维度详细说明

- **合作分析：**
  - 合作倾向评分（1-10分制）
  - 合作相关标签展示（如"近期活跃"、"有联系方式"等）
  - CPM价格预估和视频植入价格范围
  - 全球及所在国家粉丝数排名

### 2.2 内容数据模块

#### 2.2.1 内容表现指标
- **互动率分析：**
  - 综合互动率及其表现等级
  - 细分指标：观看量/粉丝数比例、点赞/观看比例、评论/观看比例、分享/观看比例
  - 每个指标显示具体数值、百分比和等级评价

- **表现等级颜色编码：**
  - 优秀：绿色
  - 良好：蓝色
  - 中等：橙色
  - 较差：红色

#### 2.2.2 互动趋势分析
- **多维度趋势图：**
  - 同一图表显示观看数、点赞数、评论数、分享数的时间变化
  - 支持图例控制显示/隐藏特定数据线
  - 支持缩放和平移查看不同时间段数据

- **发布分析：**
  - 月度发布日历热力图，显示每日发布频率
  - 周度发布频率柱状图，显示周一至周日的发布偏好
  - 最佳发布时间建议（基于互动表现）

#### 2.2.3 内容标签分析
- **标签云展示：**
  - 词云形式展示达人常用标签
  - 标签大小反映使用频率
  - 支持点击标签查看相关内容
  - 参考NOX示例：显示达人名称、品牌名称等相关标签

- **Top标签排行：**
  - 列表形式显示使用频率最高的标签
  - 显示标签名称、使用次数和占比
  - 支持查看标签相关的具体内容
  - 参考NOX示例：
    - `#barbarapalvin`: 11.4%
    - `#barbara`: 11.4%
    - `#foryoupage`: 8.1%
    - `#photoshoot`: 7.3%

- **网红分类标签：**
  - 显示15个或更多标签的完整列表
  - 支持标签的筛选和搜索功能
  - 标签按使用频率排序显示

#### 2.2.4 内容列表展示
- **统计信息：**
  - 已收录作品总数
  - 总浏览量、总点赞量、总评论数

- **内容流展示：**
  - 网格布局展示视频缩略图
  - 每个内容显示：发布时间、观看量、点赞数、评论数
  - 支持按时间、热度、互动量等排序
  - 支持内容筛选和搜索功能

### 2.3 受众数据模块

#### 2.3.1 受众核心指标
- **粉丝可信度评估：**
  - 可信度评分（1-5分制）
  - 真实粉丝比例预估
  - 粉丝质量等级标识

- **受众基本画像：**
  - 主要受众地区（含国旗图标）
  - 主要受众性别及比例
  - 主要受众年龄段

#### 2.3.2 地理分布分析
- **全球地图可视化：**
  - 世界地图显示粉丝地理分布
  - 不同颜色深度表示粉丝密度
  - 支持鼠标悬停查看具体数据

- **国家/地区排行：**
  - 条形图显示Top10粉丝来源国家
  - 显示国家名称、粉丝数量和占比
  - 支持查看更详细的地区分布数据

#### 2.3.3 语言分布分析
- **语言偏好图表：**
  - 水平条形图显示粉丝语言分布
  - 支持查看详细的语言使用统计
  - 为多语言营销提供数据支持

#### 2.3.4 人口统计分析
- **性别分布：**
  - 环形图显示男女粉丝比例
  - 数值和百分比显示

- **年龄分布：**
  - 分组柱状图显示不同年龄段粉丝分布
  - 支持男女分组对比显示
  - 显示主要年龄段和占比

#### 2.3.5 营销价值分析
- **受众反馈指标：**
  - 正向反馈受众比例（参考NOX示例：69%）
  - 对推广内容感兴趣的受众比例（参考NOX示例：65%）
  - 推广吸引度和专业度评分（星级评价）

- **内容兴趣分析：**
  - 环形图显示粉丝兴趣内容分布
  - 主要兴趣类别及占比，参考NOX示例：
    - 彩妆：60%
    - 街拍：13.3%
    - 电视&动画：13.3%
    - 电脑配件：6.6%
    - 宠物&动物：6.6%
  - 兴趣点详细描述文字

#### 2.3.6 粉丝质量评估
- **粉丝类型分析：**
  - 环形图显示不同类型粉丝比例：
    - 普通粉丝（真实用户）
    - 可疑粉丝（可能的机器人）
    - 僵尸粉（不活跃账号）
    - 网红粉（其他创作者）

- **真实度评估：**
  - 综合真实受众比例
  - 粉丝质量评级和建议

### 2.4 品牌数据模块

#### 2.4.1 商业表现指标
- **品牌推广基础数据：**
  - 品牌广告效果（互动率）
  - 品牌内容发布频次
  - 推广与非推广内容表现对比

- **商业化程度分析：**
  - 商业内容占比
  - 推广频率趋势
  - 商业化成熟度评估

#### 2.4.2 推广效果对比
- **内容类型对比图表：**
  - 对比推广内容和非推广内容的平均观看量
  - 柱状图或折线图显示效果差异
  - 提供推广效果评估建议

#### 2.4.3 品牌提及分析
- **品牌合作历史：**
  - 表格形式展示合作过的品牌
  - 包含字段：品牌Logo、品牌名称、合作次数、互动率、观看量、最后合作时间、预估费用
  - 支持按不同维度排序和筛选

- **品牌提及详情：**
  - 每个品牌的合作视频封面展示
  - 支持点击查看具体合作内容
  - 品牌合作时间线展示

#### 2.4.4 智能品牌识别（AI功能）
- **自动品牌提及检测：**
  - 基于LLM分析达人历史内容
  - 识别显性和隐性品牌提及
  - 分析品牌提及类型和情感倾向

- **品牌分析结果：**
  - 品牌提及总数和多样性评分
  - 商业内容比例分析
  - 最常合作品牌识别

### 2.5 多平台账号关联功能

#### 2.5.1 平台账号统一展示
- **关联平台图标：**
  - 在达人基本信息区域显示所有关联平台图标
  - 支持点击切换不同平台的详细数据
  - 显示各平台的活跃状态标识

- **主平台标识：**
  - 突出显示主要平台（数据最全面的平台）
  - 默认展示主平台的详细数据
  - 提供平台间数据对比功能

#### 2.5.2 跨平台数据对比
- **平台选择器：**
  - 下拉菜单或标签页形式切换平台
  - 支持多平台数据同屏对比
  - 显示各平台的数据完整度

- **数据同步状态：**
  - 显示各平台数据的最后更新时间
  - 提供手动刷新功能
  - 数据异常时的提示信息

#### 2.5.3 账号关联规则
- **基于现有数据库设计：**
  - 通过influencers表的associatedPlatforms字段管理平台关联
  - 通过platforms表的influencerId字段建立详细关联
  - 支持mainPlatform字段确定主要展示平台

### 2.6 数据获取与API接口

#### 2.6.1 数据获取接口规范
- **核心接口列表：**
  - `GET /api/influencer/{id}/overview` - 获取达人总览数据
  - `GET /api/influencer/{id}/content` - 获取内容数据
  - `GET /api/influencer/{id}/audience` - 获取受众数据
  - `GET /api/influencer/{id}/brands` - 获取品牌数据
  - `GET /api/influencer/{id}/platforms` - 获取多平台数据
  - `POST /api/influencer/{id}/analyze` - 触发品牌分析

#### 2.6.2 数据同步策略
- **实时同步数据：**
  - 粉丝数量、最新发布时间等基础指标
  - 最新内容的互动数据
  - 关键指标的实时更新

- **定时同步数据：**
  - 受众分析数据（每周更新）
  - 历史趋势数据（每日更新）
  - 品牌合作分析（每月更新）

- **按需同步数据：**
  - 详细的内容分析
  - 深度的受众洞察
  - 复杂的品牌关系分析

#### 2.6.3 接口性能要求
- **响应时间：** 所有接口响应时间不超过2秒
- **并发支持：** 支持100个并发请求
- **数据缓存：** 对重复查询进行缓存优化
- **错误处理：** 完善的错误提示和降级方案

#### 2.6.4 性能监控和评估
- **关键性能指标（KPI）：**
  - 平均响应时间 < 1.5秒
  - 95%请求响应时间 < 2秒
  - 系统可用性 > 99.9%
  - 数据准确性 > 95%

- **缓存策略：**
  - Redis缓存常用查询结果（TTL: 5分钟）
  - 浏览器缓存静态资源（TTL: 24小时）
  - CDN缓存图片和视频资源

- **数据同步性能：**
  - 实时数据同步延迟 < 10秒
  - 定时同步任务完成时间 < 30分钟
  - 增量同步数据准确率 > 98%

### 2.7 品牌提及智能分析系统

#### 2.7.1 LLM分析输入规范
- **数据输入格式：**
```json
{
  "influencer_id": "string",
  "video_data": [
    {
      "video_id": "string",
      "title": "string",
      "description": "string",
      "subtitles": "string",
      "comments": ["string"],
      "publish_date": "date",
      "platform": "string"
    }
  ]
}
```

- **分析触发机制：**
  - 新内容发布后自动触发分析
  - 用户手动触发全量重新分析
  - 定期批量分析历史内容

#### 2.7.2 LLM分析输出规范
- **分析结果格式：**
```json
{
  "influencer_id": "string",
  "brand_mentions": [
    {
      "brand_name": "string",
      "brand_category": "string",
      "mention_type": "explicit|implicit|product_placement",
      "confidence_score": "float",
      "video_id": "string",
      "mention_context": "string",
      "sentiment": "positive|neutral|negative",
      "timestamp": "string"
    }
  ],
  "analysis_summary": {
    "total_brands": "integer",
    "most_mentioned_brand": "string",
    "brand_diversity_score": "float",
    "commercial_content_ratio": "float"
  }
}
```

#### 2.7.3 Prompt设计要求
- **分析能力要求：**
  - 支持英语、中文、西班牙语等多语言品牌识别
  - 能够识别显性提及（直接提及品牌名）
  - 能够识别隐性提及（产品植入、背景露出）
  - 理解上下文语境和情感倾向

- **准确性要求：**
  - 品牌识别准确率需达到85%以上
  - 误报率控制在10%以内
  - 支持人工审核和纠正机制

- **性能要求：**
  - 单个视频分析时间不超过30秒
  - 批量分析支持并发处理（最多10个并发）
  - 提供分析进度反馈（实时进度条）
  - 分析任务队列支持优先级调度

- **多语言支持详细要求：**
  - **主要语言：** 英语、中文、西班牙语、法语、德语、日语、韩语
  - **语言检测：** 自动检测内容语言并选择相应分析模型
  - **跨语言品牌识别：** 支持品牌名称的多语言变体识别
  - **本地化品牌库：** 维护不同地区的本地化品牌数据库

- **上下文理解能力要求：**
  - **显性提及：** 直接提及品牌名称（准确率>90%）
  - **隐性提及：** 产品植入、背景露出识别（准确率>80%）
  - **情感分析：** 品牌提及的情感倾向分析（准确率>85%）
  - **语境理解：** 区分正面推广和负面评价

#### 2.7.4 品牌数据库建设
- **品牌信息维护：**
  - 建立全球品牌数据库
  - 包含品牌名称、别名、Logo、分类等信息
  - 支持品牌信息的动态更新

- **品牌匹配规则：**
  - 精确匹配品牌官方名称
  - 模糊匹配品牌常用别名
  - 通过Logo识别进行视觉匹配

---

## 3. 技术实现方案

### 3.1 前端技术架构

#### 3.1.1 组件化设计
- **模块化组件：**
  - 数据总览组件（OverviewModule）
  - 内容数据组件（ContentModule）
  - 受众数据组件（AudienceModule）
  - 品牌数据组件（BrandModule）
  - 多平台切换组件（PlatformSwitcher）

- **图表组件库：**
  - 集成Chart.js或ECharts等图表库
  - 封装常用图表类型（折线图、柱状图、环形图、雷达图等）
  - 支持响应式和交互功能

#### 3.1.2 数据状态管理
- **状态管理方案：**
  - 使用Redux或Vuex进行状态管理
  - 分模块管理不同类型的数据
  - 实现数据缓存和更新机制

- **数据加载策略：**
  - 懒加载非关键数据
  - 实现骨架屏和加载状态
  - 错误状态的友好提示

### 3.2 后端技术架构

#### 3.2.1 数据库设计优化
- **基于现有表结构扩展：**
  - 扩展influencers表字段
  - 新增brand_mentions表
  - 优化platforms表关联关系

- **索引优化：**
  - 为高频查询字段创建索引
  - 优化多表联合查询性能
  - 实现数据分页和分片

#### 3.2.2 API接口设计
- **RESTful API设计：**
  - 遵循REST设计原则
  - 统一的响应格式
  - 完善的错误处理机制

- **数据获取优化：**
  - 实现数据预取和缓存
  - 支持增量数据更新
  - 优化大量数据的传输

### 3.3 AI分析系统

#### 3.3.1 LLM集成方案
- **模型选择：**
  - 推荐使用GPT-4或Claude等先进模型
  - 支持多模型对比和切换
  - 实现模型输出的一致性验证

- **Prompt工程：**
  - 设计专业的品牌识别Prompt
  - 实现Few-shot学习优化
  - 支持Prompt版本管理和A/B测试

#### 3.3.2 分析任务调度
- **任务队列系统：**
  - 使用Redis或RabbitMQ实现任务队列
  - 支持任务优先级和重试机制
  - 实现分析进度的实时反馈

- **批处理优化：**
  - 支持批量内容分析
  - 实现分析结果的增量更新
  - 优化分析成本和效率

---

## 4. 用户体验设计

### 4.1 界面设计原则

#### 4.1.1 信息层次设计
- **关键信息突出：**
  - 使用卡片式布局突出重要数据
  - 通过颜色和大小区分信息重要性
  - 实现信息的逐层展示

- **导航便捷性：**
  - 提供快速导航菜单
  - 支持模块间快速切换
  - 实现面包屑导航

#### 4.1.2 数据可视化设计
- **图表一致性：**
  - 统一的颜色主题和风格
  - 一致的交互方式
  - 标准化的图例和标签

- **响应式设计：**
  - 适配不同屏幕尺寸
  - 移动端优化显示
  - 支持触摸操作

#### 4.1.3 图表类型规范
- **环形图（Donut Chart）：**
  - 用途：性别分布、内容分布、粉丝类型分布
  - 交互：悬停显示具体数值和百分比
  - 颜色：使用语义化颜色（绿色=正面，红色=负面）

- **柱状图（Bar Chart）：**
  - 用途：年龄分布、发布频率、品牌合作次数对比
  - 交互：点击柱状图查看详细数据
  - 排序：支持按数值大小排序

- **折线图（Line Chart）：**
  - 用途：粉丝增长趋势、互动趋势、发布时间序列
  - 交互：缩放、平移、数据点详情查看
  - 多线图：支持多个指标同时展示

- **雷达图（Radar Chart）：**
  - 用途：频道质量多维度评估
  - 交互：鼠标悬停查看维度说明
  - 评分：1-5分制，支持半分显示

- **热力图（Heatmap）：**
  - 用途：发布时间分布、地理分布密度
  - 交互：点击热点区域查看详细信息
  - 颜色梯度：使用连续色彩表示数据密度

- **词云图（Word Cloud）：**
  - 用途：标签分布、关键词分析
  - 交互：点击词语查看相关内容
  - 字体大小：根据使用频率动态调整

### 4.2 交互体验优化

#### 4.2.1 加载体验
- **渐进式加载：**
  - 关键数据优先加载
  - 使用骨架屏提升感知性能
  - 实现数据的流式加载

- **缓存策略：**
  - 智能缓存常用数据
  - 实现离线数据浏览
  - 优化网络请求频率

#### 4.2.2 操作反馈
- **实时反馈：**
  - 操作结果的即时反馈
  - 加载状态的明确提示
  - 错误信息的友好展示

- **引导提示：**
  - 新功能的操作指导
  - 复杂功能的使用说明
  - 数据解读的帮助信息

#### 4.2.3 交互功能规范
- **数据钻取功能：**
  - 点击图表元素查看详细数据
  - 支持多层级数据钻取（总览→详情→明细）
  - 提供面包屑导航返回上级

- **时间筛选功能：**
  - 支持预设时间范围（7天、30天、90天、1年）
  - 自定义时间范围选择器
  - 时间对比功能（同比、环比）

- **数据导出功能：**
  - Excel格式导出（.xlsx）
  - PDF报告导出（包含图表）
  - 图片格式导出（.png, .jpg）
  - 支持批量导出多个数据集

- **实时刷新机制：**
  - 自动刷新间隔：5分钟（可配置）
  - 手动刷新按钮
  - 数据更新时的动画效果
  - 显示最后更新时间

- **筛选和搜索功能：**
  - 全局搜索框（支持品牌名、标签搜索）
  - 多维度筛选器（时间、平台、分类等）
  - 筛选条件保存和恢复
  - 高级搜索功能（正则表达式支持）

---

## 5. 实施计划

### 5.1 开发阶段规划

#### 5.1.1 第一阶段（P0 - 基础功能）
**时间：4-6周**
- 数据总览模块开发
- 基础图表组件实现
- 多平台账号关联展示
- 基础API接口开发

#### 5.1.2 第二阶段（P1 - 核心功能）
**时间：6-8周**
- 内容数据和受众数据模块
- 品牌提及LLM分析功能
- 高级图表交互功能
- 数据可视化优化

#### 5.1.3 第三阶段（P2 - 完善功能）
**时间：4-6周**
- 数据API接口完善
- 实时数据同步系统
- 移动端适配优化
- 性能优化和测试

#### 5.1.4 优先级评估标准
- **P0（必须有）- 基础功能：**
  - 用户体验影响：直接影响核心使用流程
  - 技术复杂度：中等，基于现有技术栈
  - 业务价值：高，提供基础数据展示能力
  - 开发周期：4-6周

- **P1（应该有）- 增强功能：**
  - 用户体验影响：显著提升用户体验
  - 技术复杂度：高，需要AI技术集成
  - 业务价值：高，提供智能化分析能力
  - 开发周期：6-8周

- **P2（可以有）- 完善功能：**
  - 用户体验影响：锦上添花，提升易用性
  - 技术复杂度：中等，主要是优化和扩展
  - 业务价值：中等，提升系统完整性
  - 开发周期：4-6周

- **优先级调整标准：**
  - **用户反馈权重：** 40%
  - **技术可行性权重：** 30%
  - **业务价值权重：** 20%
  - **开发成本权重：** 10%

### 5.2 技术里程碑

#### 5.2.1 关键技术节点
- **数据库设计完成**（第1周）
- **基础API接口开发完成**（第3周）
- **LLM分析系统集成完成**（第6周）
- **前端主要模块开发完成**（第8周）
- **系统集成测试完成**（第12周）

#### 5.2.2 质量保证计划
- **单元测试覆盖率达到80%**
- **接口性能测试通过**
- **用户体验测试验收**
- **数据准确性验证**

### 5.3 风险评估与应对

#### 5.3.1 技术风险
- **LLM分析准确性风险：**
  - 建立测试数据集验证准确率
  - 实现人工审核机制
  - 设计备用分析方案

- **数据量性能风险：**
  - 实现数据分页和懒加载
  - 优化数据库查询性能
  - 设计数据缓存策略

#### 5.3.2 业务风险
- **用户体验风险：**
  - 进行用户测试验证
  - 实现渐进式功能发布
  - 收集用户反馈持续优化

- **数据准确性风险：**
  - 建立数据质量监控
  - 实现数据更新验证
  - 设计数据纠错机制

---

## 6. 数据库设计扩展

### 6.1 platforms表字段扩展设计

基于NOX调研功能需求，需要在现有platforms表基础上新增以下字段：

#### 6.1.1 达人总预览表（基础信息+核心指标）

| 字段名 | 数据类型 | 描述 | 示例值 | 数据来源 | 字段状态 |
|--------|----------|------|--------|-----------|----------|
| `_id` | String | 记录唯一标识 | MongoDB ObjectId | 系统生成 | **已有字段** |
| `influencerId` | String | 关联达人ID | "influencer_123" | 关联字段 | **已有字段** |
| `influencerName` | String | 达人名称 | "张三" | 原始数据 | **已有字段** |
| `platform` | String | 平台名称 | "tiktok" | 原始数据 | **已有字段** |
| `profileUrl` | String | 达人主页链接 | "https://..." | 原始数据 | **已有字段** |
| `subscribers` | Integer | 粉丝数量 | 111256 | 原始数据 | **已有字段** |
| `lastPostTime` | Date | 最近发布时间 | "2025-07-15" | 从contents表中查询最新记录的publishDate字段 | 新增字段 |
| `lastPromoTime` | Date | 最近推广时间 | "2025-03-08" | 从contents表中查询promotedProductCategory不为空或标题包含推广关键词的最新记录日期 | 新增字段 |
| `noxScore` | Float | Nox综合评分(1-5分) | 2.7 | 取粉丝增长、发布频率、内容质量、互动率、真实性、合作历史六个维度评分的加权平均值 | 新增字段 |
| `collaborationTendency` | Integer | 合作倾向评分(1-10分) | 2 | 综合活跃度、邮件回复率、历史合作次数、联系方式可用性、受众质量进行加权评分 | 新增字段 |

#### 6.1.2 数据总览表（统计指标和排名）

| 字段名 | 数据类型 | 描述 | 示例值 | 数据来源 | 字段状态 |
|--------|----------|------|--------|-----------|----------|
| `noxScoreDetails` | JSON | 雷达图各维度评分 | {"growth": 3, "frequency": 2, "quality": 3, "engagement": 2, "authenticity": 3, "cooperation": 1} | 根据粉丝数健、发布频率、观看量、互动率、真实性评分等各维度单独评分后组成JSON格式 | 新增字段 |
| `engagementRate` | Float | 互动率 | 0.0613 | 点赞数+评论数/观看量 | **已有字段** |
| `recentVideoPlays` | Integer | 近期视频播放量 | 101000 | 原始数据 | **已有字段** |
| `avgViews30d` | Integer | 近30天平均观看量 | 101000 | 从contents表中取近30天内所有视频的views字段平均值 | 新增字段 |
| `avgEngagement30d` | Integer | 近30天平均互动量 | 5507 | 从contents表中取近30天内所有视频的likes+comments字段平均值 | 新增字段 |
| `contentCount30d` | Integer | 近30天内容数量 | 30 | 从contents表中统计近30天内publishDate字段的记录总数 | 新增字段 |
| `viewsPerFollowerRatio` | Float | 观看量/粉丝数比例 | 0.9059 | 用avgViews30d字段除以subscribers字段得出的比例 | 新增字段 |
| `globalRank` | Integer | 全球粉丝数排名 | 1457910 | 在全部平台记录中按subscribers字段降序排列后的排名位置 | 新增字段 |
| `countryRank` | Integer | 所在国家粉丝数排名 | 2617 | 在同一国家的平台记录中按subscribers字段降序排列后的排名位置 | 新增字段 |
| `collaborationLabels` | Array[String] | 合作相关标签 | ["近期活跃", "有联系方式", "回复率低"] | **LLM增强** | 新增字段 |
| `estimatedCPM` | Float | 预估CPM价格 | 22.5 | 基于平台基础CPM价格乘以粉丝数量系数、互动率系数、质量系数得出 | 新增字段 |
| `estimatedVideoPrice` | String | 视频植入价格范围 | "1610-2187" | 用estimatedCPM乘以平均观看量再除以1000，上下浮动20%形成价格区间 | 新增字段 |
| `followerGrowthTrend` | JSON | 粉丝增长趋势数据 | [{"date": "2025-07-01", "count": 111200}] | 从platforms表的历史记录中按日期统计subscribers字段形成时间序列数据 | 新增字段 |
| `likesGrowthTrend` | JSON | 点赞增长趋势数据 | [{"date": "2025-07-01", "count": 1795000}] | 从platforms表的历史记录中按日期统计totalLikes字段形成时间序列数据 | 新增字段 |
| `videoGrowthTrend` | JSON | 视频增长趋势数据 | [{"date": "2025-07-01", "count": 240}] | 从contents表中按publishDate统计每日的累计视频数量形成时间序列数据 | 新增字段 |
| `engagementGrowthTrend` | JSON | 互动增长趋势数据 | [{"date": "2025-07-01", "rate": 0.061}] | 从contents表中按publishDate统计每日的平均互动率形成时间序列数据 | 新增字段 |

#### 6.1.3 内容数据表（内容表现和发布分析）

| 字段名 | 数据类型 | 描述 | 示例值 | 数据来源 | 字段状态 |
|--------|----------|------|--------|-----------|----------|
| `enhancedTags` | Array[String] | 增强标签 | ["#fashion", "#beauty"] | **LLM增强** | **已有字段** |
| `categoryDepth` | String | 分类深度 | "Beauty > Makeup > Foundation" | **LLM增强** | **已有字段** |
| `engagementRate30d` | Float | 近30天互动率 | 0.0613 | 从contents表中取近30天内所有视频的likes+comments总和除以views总和得出 | 新增字段 |
| `likesPerView30d` | Float | 点赞/观看比例 | 0.0547 | 从contents表中取近30天内所有视频的likes总和除以views总和得出 | 新增字段 |
| `commentsPerView30d` | Float | 评论/观看比例 | 0.0004 | 从contents表中取近30天内所有视频的comments总和除以views总和得出 | 新增字段 |
| `sharesPerView30d` | Float | 分享/观看比例 | 0.0008 | 从contents表中取近30天内所有视频的分享数总和除以views总和得出 | 新增字段 |
| `interactionTrendData` | JSON | 互动趋势图数据 | [{"date": "2025-07-01", "views": 10000, "likes": 500, "comments": 20}] | 从contents表中按publishDate排序组成包含日期、观看量、点赞、评论数的JSON数组 | 新增字段 |
| `publishCalendar` | JSON | 发布日历数据 | {"2025-07-13": 1, "2025-07-14": 2} | 从contents表中按publishDate字段的日期部分统计每日发布数量的JSON对象 | 新增字段 |
| `publishFrequencyWeek` | JSON | 周度发布频率 | {"monday": 15.4, "tuesday": 12.3, "wednesday": 18.2} | 从contents表中按publishDate字段的星期部分统计各星期发布数量的百分比 | 新增字段 |
| `topHashtags` | JSON | Top标签及占比 | [{"tag": "#barbarapalvin", "count": 25, "percentage": 0.114}] | 从contents表的enhancedTags字段中统计所有标签出现次数并计算占比 | 新增字段 |
| `totalArchivedVideos` | Integer | 已收录作品总数 | 137 | 从contents表中统计该达人的所有记录数 | 新增字段 |
| `totalArchivedViews` | Integer | 已收录作品总观看 | 15881300 | 从contents表中求该达人所有记录的views字段总和 | 新增字段 |
| `totalArchivedLikes` | Integer | 已收录作品总点赞 | 1285600 | 从contents表中求该达人所有记录的likes字段总和 | 新增字段 |
| `totalArchivedComments` | Integer | 已收录作品总评论 | 6039 | 从contents表中求该达人所有记录的comments字段总和 | 新增字段 |

#### 6.1.4 受众数据表（粉丝画像和质量分析）

| 字段名 | 数据类型 | 描述 | 示例值 | 数据来源 | 字段状态 |
|--------|----------|------|--------|-----------|----------|
| `audienceGender` | String | 受众性别倾向 | "female" | **LLM增强** | **已有字段** |
| `audienceAge` | String | 受众年龄倾向 | "25-34" | **LLM增强** | **已有字段** |
| `authenticityScore` | Float | 粉丝可信度评分(1-5分) | 2.5 | **LLM增强** | 新增字段 |
| `realFollowerRatio` | Float | 真实粉丝比例 | 0.825 | **LLM增强** | 新增字段 |
| `topAudienceCountry` | String | 最多受众国家 | "捷克共和国" | **LLM增强** | 新增字段 |
| `topAudienceGender` | String | 最多受众性别 | "女性" | **LLM增强** | 新增字段 |
| `topAudienceAge` | String | 最多受众年龄段 | "25-34" | **LLM增强** | 新增字段 |
| `geoDistribution` | JSON | 受众地理分布 | [{"country": "Czech Republic", "percentage": 0.248}] | **LLM增强** | 新增字段 |
| `languageDistribution` | JSON | 受众语言分布 | [{"language": "Danish", "percentage": 0.529}] | **LLM增强** | 新增字段 |
| `genderDistribution` | JSON | 性别分布 | {"female": 0.778, "male": 0.222} | **LLM增强** | 新增字段 |
| `ageDistribution` | JSON | 年龄分布 | [{"range": "25-34", "percentage": 0.353}] | **LLM增强** | 新增字段 |
| `positiveFeedbackRatio` | Float | 正向反馈受众比例 | 0.69 | **LLM增强** | 新增字段 |
| `promoInterestRatio` | Float | 推广内容感兴趣比例 | 0.65 | **LLM增强** | 新增字段 |
| `promoAttractionScore` | Integer | 推广吸引度评分(1-5分) | 3 | **LLM增强** | 新增字段 |
| `promoProfessionalScore` | Integer | 推广专业度评分(1-5分) | 3 | **LLM增强** | 新增字段 |
| `interestDistribution` | JSON | 粉丝兴趣分布 | [{"interest": "彩妆", "percentage": 0.60}] | **LLM增强** | 新增字段 |
| `followerTypeDistribution` | JSON | 粉丝类型分布 | {"normal": 0.80, "suspicious": 0.119, "zombie": 0.056, "influencer": 0.025} | **LLM增强** | 新增字段 |

#### 6.1.5 品牌数据表（商业合作和品牌分析）

| 字段名 | 数据类型 | 描述 | 示例值 | 数据来源 | 字段状态 |
|--------|----------|------|--------|-----------|----------|
| `promotedProductCategory` | String | 推广产品类别 | "Beauty" | 原始数据 | **已有字段** |
| `promotionAbility` | Float | 推广能力评分 | 0.75 | 综合评分 | **已有字段** |
| `brandedAdEngagement` | Float | 品牌广告互动率 | 0.045 | 从contents表中查询promotedProductCategory不为空的记录，计算其likes+comments总和除以views总和 | 新增字段 |
| `brandedAdFrequency` | String | 品牌广告发布频次 | "2/月" | 统计近90天内promotedProductCategory不为空的记录数量除以3得出月均值 | 新增字段 |
| `brandedVsNonBrandedData` | JSON | 推广vs非推广数据对比 | {"branded_avg_views": 80000, "non_branded_avg_views": 102000} | 分别统计promotedProductCategory为空和不为空的记录的平均观看量形成对比数据 | 新增字段 |
| `brandMentions` | JSON | 品牌提及分析结果 | [{"brand": "Victoria's Secret", "count": 9, "engagement": 0.058, "total_views": 346700}] | **LLM增强** | 新增字段 |
| `commercialContentRatio` | Float | 商业内容占比 | 0.15 | 统计近90天内promotedProductCategory不为空的记录数量除以总记录数量 | 新增字段 |
| `brandDiversityScore` | Float | 品牌多样性评分 | 0.75 | 从brand_mentions表中统计不同品牌数量除以总提及次数，乘以品牌分散度系数 | 新增字段 |
| `mostMentionedBrand` | String | 最常提及品牌 | "Victoria's Secret" | 从brand_mentions表中按brandName统计提及次数最多的品牌名称 | 新增字段 |

### 7.1 计算得出字段的计算公式

#### 7.1.1 数据总览模块计算公式

**noxScore (综合评分)**
```
noxScore = (
    followerGrowthScore * 0.2 + 
    publishFrequencyScore * 0.15 + 
    contentQualityScore * 0.2 + 
    engagementScore * 0.2 + 
    authenticityScore * 0.15 + 
    collaborationScore * 0.1
) / 5.0

// 各维度评分计算：
// followerGrowthScore = min(5, max(1, log10(30天粉丝增长率 * 1000) + 1))
// publishFrequencyScore = min(5, max(1, 平均日发布数 * 2))
// contentQualityScore = min(5, max(1, 平均观看量 / 粉丝数 * 5))
// engagementScore = min(5, max(1, 互动率 * 50))
// authenticityScore = 粉丝可信度评分 (LLM提供)
// collaborationScore = min(5, max(1, 历史合作次数 / 5))
```

**collaborationTendency (合作倾向)**
```
collaborationTendency = (
    activityFactor * 0.3 + 
    responseFactor * 0.25 + 
    cooperationHistory * 0.2 + 
    contactAvailability * 0.15 + 
    audienceQuality * 0.1
) * 10

// 各因子计算：
// activityFactor = min(1, 近7天发布数 / 3)
// responseFactor = min(1, 历史邮件回复率)
// cooperationHistory = min(1, 历史合作次数 / 10)
// contactAvailability = contactInfo 不为空 ? 1 : 0.3
// audienceQuality = authenticityScore / 5
```

**estimatedCPM (CPM价格预估)**
```
baseCPM = {
    "youtube": 15,
    "tiktok": 8,
    "instagram": 12
}

followerMultiplier = {
    "< 10K": 0.5,
    "10K-100K": 1.0,
    "100K-1M": 1.5,
    "> 1M": 2.0
}

engagementMultiplier = min(2.0, max(0.5, engagementRate * 20))
qualityMultiplier = noxScore / 3

estimatedCPM = baseCPM[platform] * followerMultiplier * engagementMultiplier * qualityMultiplier
```

**estimatedVideoPrice (视频植入价格)**
```
priceMin = estimatedCPM * avgViews30d / 1000 * 0.8
priceMax = estimatedCPM * avgViews30d / 1000 * 1.2
estimatedVideoPrice = `${priceMin.toFixed(0)}-${priceMax.toFixed(0)}`
```

**lastPromoTime (最近推广时间)**
```
// 从 contents 表查询最近的推广内容
lastPromoTime = (
    SELECT MAX(publishDate) 
    FROM contents 
    WHERE influencerId = ? 
    AND (
        promotedProductCategory IS NOT NULL 
        OR title LIKE '%广告%' 
        OR title LIKE '%合作%'
        OR title LIKE '%推广%'
    )
)
```

#### 7.1.2 内容数据模块计算公式

**engagementRate30d (近30天互动率)**
```
engagementRate30d = (
    SUM(likes + comments + shares) / SUM(views)
) FROM contents 
WHERE influencerId = ? 
AND publishDate >= DATE_SUB(NOW(), INTERVAL 30 DAY)
```

**interactionTrendData (互动趋势数据)**
```
interactionTrendData = [
    {
        "date": publishDate,
        "views": views,
        "likes": likes,
        "comments": comments,
        "shares": shares,
        "engagement_rate": (likes + comments + shares) / views
    }
    FROM contents 
    WHERE influencerId = ? 
    AND publishDate >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    ORDER BY publishDate DESC
]
```

**publishCalendar (发布日历)**
```
publishCalendar = {
    DATE(publishDate): COUNT(*)
    FROM contents 
    WHERE influencerId = ? 
    AND publishDate >= DATE_SUB(NOW(), INTERVAL 90 DAY)
    GROUP BY DATE(publishDate)
}
```

**topHashtags (Top标签统计)**
```
// 从 contents 表的 enhancedTags 字段统计
topHashtags = [
    {
        "tag": tag,
        "count": COUNT(*),
        "percentage": COUNT(*) / total_contents
    }
    FROM (
        SELECT TRIM(tag) as tag
        FROM contents, JSON_TABLE(enhancedTags, '$[*]' COLUMNS (tag VARCHAR(255) PATH '$')) as jt
        WHERE influencerId = ?
    ) as tag_counts
    GROUP BY tag
    ORDER BY COUNT(*) DESC
    LIMIT 20
]
```

#### 7.1.3 品牌数据模块计算公式

**brandedAdEngagement (品牌广告互动率)**
```
brandedAdEngagement = (
    SUM(likes + comments + shares) / SUM(views)
) FROM contents 
WHERE influencerId = ? 
AND promotedProductCategory IS NOT NULL
AND publishDate >= DATE_SUB(NOW(), INTERVAL 90 DAY)
```

**commercialContentRatio (商业内容占比)**
```
commercialContentRatio = (
    SELECT COUNT(*) FROM contents 
    WHERE influencerId = ? 
    AND promotedProductCategory IS NOT NULL
    AND publishDate >= DATE_SUB(NOW(), INTERVAL 90 DAY)
) / (
    SELECT COUNT(*) FROM contents 
    WHERE influencerId = ? 
    AND publishDate >= DATE_SUB(NOW(), INTERVAL 90 DAY)
)
```

**brandDiversityScore (品牌多样性评分)**
```
brandDiversityScore = (
    DISTINCT_BRANDS_COUNT / TOTAL_BRAND_MENTIONS
) * (
    1 - (MAX_BRAND_MENTIONS / TOTAL_BRAND_MENTIONS)
)

// 基于 brand_mentions 表计算品牌多样性
// 值越高表示品牌越多样化，不依赖单一品牌
```

### 7.2 LLM增强字段的Prompt设计

#### 7.2.1 合作标签分析Prompt

**用途：**生成 collaborationLabels 字段

```
You are an expert influencer marketing analyst. Based on the provided influencer data, generate collaboration-related labels that help brands understand this influencer's cooperation potential.

**Input Data:**
- Platform: {platform}
- Follower Count: {subscribers}
- Recent Activity: {isActive}
- Last Post Date: {lastPostDate}
- Contact Info Available: {hasContactInfo}
- Engagement Rate: {engagementRate}
- Previous Collaborations: {previousCollaborations}
- Response History: {responseHistory}
- Content Quality: {contentQuality}

**Required Output Format:**
["label1", "label2", "label3", ...]

**Label Categories and Criteria:**

1. **Activity Level:**
   - "高频活跃" (posts >3 times/week)
   - "正常活跃" (posts 1-3 times/week)
   - "低频活跃" (posts <1 time/week)
   - "近期休眠" (no posts in 30 days)

2. **Contact Accessibility:**
   - "有联系方式" (contact info available)
   - "联系困难" (no contact info)
   - "经纪人代理" (managed by agency)

3. **Collaboration History:**
   - "合作经验丰富" (>5 previous collaborations)
   - "有合作经验" (1-5 previous collaborations)
   - "新手博主" (no collaboration history)

4. **Response Quality:**
   - "回复积极" (response rate >80%)
   - "回复一般" (response rate 50-80%)
   - "回复率低" (response rate <50%)

5. **Content Quality:**
   - "内容优质" (high engagement, professional content)
   - "内容中等" (average engagement)
   - "内容待提升" (low engagement, poor quality)

6. **Audience Quality:**
   - "粉丝质量高" (authenticity score >4)
   - "粉丝质量中等" (authenticity score 2-4)
   - "粉丝质量待验证" (authenticity score <2)

**Instructions:**
1. Analyze the input data carefully
2. Select 3-5 most relevant labels
3. Prioritize labels that are most useful for brand decision-making
4. Ensure labels are accurate and evidence-based
5. Return only the JSON array of labels

**Example Output:**
["高频活跃", "有联系方式", "合作经验丰富", "回复积极", "粉丝质量高"]
```

#### 7.2.2 受众分析Prompt

**用途：**生成所有受众相关字段

```
You are an expert social media audience analyst. Analyze the provided influencer data to generate comprehensive audience insights.

**Input Data:**
- Platform: {platform}
- Follower Count: {subscribers}
- Recent Videos: {recentVideos} // Array of video data with comments
- Engagement Data: {engagementData}
- Geographic Hints: {geoHints} // Any available location data
- Content Categories: {contentCategories}

**Required Output Format:**
{
  "authenticityScore": 0.0-5.0,
  "realFollowerRatio": 0.0-1.0,
  "topAudienceCountry": "string",
  "topAudienceGender": "string",
  "topAudienceAge": "string",
  "geoDistribution": [{"country": "string", "percentage": 0.0-1.0}],
  "languageDistribution": [{"language": "string", "percentage": 0.0-1.0}],
  "genderDistribution": {"female": 0.0-1.0, "male": 0.0-1.0},
  "ageDistribution": [{"range": "string", "percentage": 0.0-1.0}],
  "positiveFeedbackRatio": 0.0-1.0,
  "promoInterestRatio": 0.0-1.0,
  "promoAttractionScore": 1-5,
  "promoProfessionalScore": 1-5,
  "interestDistribution": [{"interest": "string", "percentage": 0.0-1.0}],
  "followerTypeDistribution": {
    "normal": 0.0-1.0,
    "suspicious": 0.0-1.0,
    "zombie": 0.0-1.0,
    "influencer": 0.0-1.0
  }
}

**Analysis Instructions:**

1. **Authenticity Score (1-5):**
   - Analyze comment patterns, engagement consistency, follower growth rate
   - Look for bot-like behavior, spam comments, unnatural engagement spikes
   - Consider engagement rate vs follower count ratio

2. **Geographic Analysis:**
   - Analyze comment languages, time zone patterns, location mentions
   - Identify primary and secondary markets
   - Consider content relevance to different regions

3. **Demographic Analysis:**
   - Analyze comment tone, language style, profile pictures
   - Identify age-specific references, interests, communication patterns
   - Look for gender-specific interests and interaction styles

4. **Interest Analysis:**
   - Analyze comment content, video topics, engagement patterns
   - Identify main interest categories from audience interactions
   - Consider seasonal trends and topic preferences

5. **Engagement Quality:**
   - Analyze comment quality, relevance, positivity
   - Identify promotional content reception
   - Assess audience receptivity to brand collaborations

6. **Follower Quality:**
   - Classify followers based on account activity, profile completeness
   - Identify potential fake accounts, inactive users
   - Assess genuine engagement vs artificial inflation

**Quality Indicators:**
- **High Quality:** Diverse, engaged comments; consistent engagement; natural growth
- **Medium Quality:** Some engagement; mixed comment quality; moderate growth
- **Low Quality:** Repetitive comments; irregular engagement; suspicious growth

**Important Notes:**
- Base analysis on observable patterns, not assumptions
- Provide realistic estimates based on available data
- Consider cultural and linguistic contexts
- Account for platform-specific audience behaviors
```

#### 7.2.3 品牌提及分析Prompt

**用途：**生成 brand_mentions 表数据和 brandMentions 字段

```
You are an expert brand mention detection system. Analyze the provided video content to identify all brand mentions and partnerships.

**Input Data:**
- Video ID: {videoId}
- Video Title: {title}
- Video Description: {description}
- Video Subtitles: {subtitles}
- Top Comments: {topComments}
- Video Metadata: {metadata}
- Platform: {platform}

**Required Output Format:**
{
  "brand_mentions": [
    {
      "brand_name": "string",
      "brand_category": "string",
      "mention_type": "explicit|implicit|product_placement",
      "confidence_score": 0.0-1.0,
      "mention_context": "string",
      "sentiment": "positive|neutral|negative",
      "timestamp": "string" // if available from subtitles
    }
  ],
  "analysis_summary": {
    "total_brands": 0,
    "most_mentioned_brand": "string",
    "brand_diversity_score": 0.0-1.0,
    "commercial_content_ratio": 0.0-1.0
  }
}

**Brand Detection Criteria:**

1. **Explicit Mentions:**
   - Direct brand name mentions in title/description/subtitles
   - Clear product endorsements
   - Sponsored content disclosures
   - Confidence: 0.8-1.0

2. **Implicit Mentions:**
   - Product placement in video content
   - Brand logos visible in background
   - Subtle brand references
   - Confidence: 0.5-0.8

3. **Product Placement:**
   - Products used without explicit mention
   - Brand items in scene
   - Lifestyle integration
   - Confidence: 0.3-0.7

**Brand Categories:**
- Fashion & Beauty
- Technology & Electronics
- Food & Beverage
- Travel & Tourism
- Health & Fitness
- Home & Garden
- Automotive
- Entertainment
- Education
- Finance
- Other

**Sentiment Analysis:**
- **Positive:** Endorsement, recommendation, positive review
- **Neutral:** Factual mention, neutral presentation
- **Negative:** Criticism, negative review, complaints

**Context Extraction:**
- Extract 2-3 sentence context around brand mention
- Include surrounding text that provides meaning
- Preserve original language and tone

**Quality Assurance:**
- Verify brand names against known brand database
- Check for spelling variations and alternate names
- Validate mention type classification
- Ensure confidence scores are realistic

**Special Instructions:**
1. Focus on legitimate brand mentions, not coincidental words
2. Consider cultural and linguistic context
3. Distinguish between organic mentions and paid partnerships
4. Account for platform-specific content patterns
5. Handle multiple languages appropriately

**Example Output:**
{
  "brand_mentions": [
    {
      "brand_name": "Nike",
      "brand_category": "Fashion & Beauty",
      "mention_type": "explicit",
      "confidence_score": 0.95,
      "mention_context": "I'm wearing these amazing Nike Air Max shoes that I absolutely love. They're so comfortable for my daily workouts.",
      "sentiment": "positive",
      "timestamp": "02:35"
    }
  ],
  "analysis_summary": {
    "total_brands": 1,
    "most_mentioned_brand": "Nike",
    "brand_diversity_score": 0.8,
    "commercial_content_ratio": 0.2
  }
}
```

#### 7.2.4 内容商业化判断Prompt

**用途：**生成 content_performance 表中的 isBranded 字段

```
You are an expert content commercialization detector. Analyze the provided video content to determine if it contains commercial/promotional elements.

**Input Data:**
- Video Title: {title}
- Video Description: {description}
- Video Tags: {tags}
- Thumbnail Analysis: {thumbnailAnalysis} // if available
- Video Duration: {duration}
- Platform: {platform}

**Required Output Format:**
{
  "is_branded": true/false,
  "confidence_score": 0.0-1.0,
  "commercial_indicators": ["string"],
  "brand_partnership_type": "sponsored|affiliate|gifted|organic|none",
  "disclosure_compliance": "compliant|partial|non_compliant|not_applicable"
}

**Commercial Content Indicators:**

1. **Explicit Commercial Signals:**
   - Sponsored content disclosures (#ad, #sponsored, #partnership)
   - Affiliate link mentions
   - Promo code references
   - Direct product promotion
   - Brand collaboration announcements

2. **Implicit Commercial Signals:**
   - Product unboxing videos
   - Product reviews with purchase links
   - Lifestyle content featuring specific brands
   - Tutorial videos using specific products
   - "Get ready with me" featuring products

3. **Visual/Textual Cues:**
   - Product placement in thumbnails
   - Brand logos prominently displayed
   - Professional product photography
   - Comparison content with purchasing advice
   - Discount/sale mentions

**Partnership Types:**
- **Sponsored:** Paid partnership with disclosure
- **Affiliate:** Commission-based promotion
- **Gifted:** Free product received for review
- **Organic:** Natural brand mention without compensation
- **None:** No commercial elements detected

**Disclosure Compliance:**
- **Compliant:** Proper disclosure visible and clear
- **Partial:** Disclosure present but unclear/hidden
- **Non-compliant:** Commercial content without disclosure
- **Not applicable:** No commercial content detected

**Analysis Instructions:**
1. Look for explicit commercial language and disclosures
2. Analyze content structure and focus
3. Check for product-centric narratives
4. Evaluate thumbnail commercial elements
5. Consider platform-specific commercial patterns
6. Assess disclosure adequacy and placement

**Confidence Scoring:**
- 0.9-1.0: Clear commercial content with explicit indicators
- 0.7-0.9: Strong commercial signals with implicit indicators
- 0.5-0.7: Moderate commercial elements, some ambiguity
- 0.3-0.5: Weak commercial signals, mostly organic
- 0.0-0.3: No commercial content detected

**Example Output:**
{
  "is_branded": true,
  "confidence_score": 0.92,
  "commercial_indicators": ["#sponsored", "promo code", "affiliate link", "product unboxing"],
  "brand_partnership_type": "sponsored",
  "disclosure_compliance": "compliant"
}
```

### 7.3 数据更新策略和执行流程

#### 7.3.1 计算字段更新策略

**实时更新字段：**
- 粉丝数量相关指标
- 最新内容互动数据
- 排名相关数据

**每日更新字段：**
- 30天平均数据
- 趋势数据
- 发布频率统计

**每周更新字段：**
- Nox综合评分
- 合作倾向评分
- 品牌合作数据

#### 7.3.2 LLM增强字段更新策略

**实时触发：**
- 新内容发布时触发品牌提及分析
- 新内容的商业化判断

**每日批量更新：**
- 受众数据分析
- 合作标签更新

**每周批量更新：**
- 粉丝质量评估
- 兴趣分布分析

#### 7.3.3 数据质量保证

**计算结果验证：**
- 范围检查：评分在有效范围内
- 逻辑检查：相关指标一致性
- 历史对比：与历史数据对比检查异常

**LLM结果验证：**
- 置信度阈值检查
- 多模型结果对比
- 人工抽样审核

**数据完整性检查：**
- 必要字段的完整性
- 关联关系的一致性
- 数据格式的正确性

---

## 8. 总结

### 7.1 项目价值

通过实施达人详情页增强功能，将显著提升用户对达人的了解深度和商业价值评估能力。智能化的品牌分析和多平台数据整合将为用户提供更全面的决策支持，提高网红营销的效率和成功率。

### 7.2 后续规划

- **数据智能化升级：** 基于用户行为优化推荐算法
- **社交网络分析：** 增加达人影响力网络分析
- **预测性分析：** 开发达人表现预测模型
- **自动化报告：** 实现达人分析报告自动生成
