# 网红营销定价与匹配系统需求文档

- 1、爬取时要记录最新 5 个视频的播放量，好滚动计算达人的视频报价。-爬虫
- 2、每周更新一次达人最新报价范围，CPM*最近 5 个视频播放均值/1000 -爬虫
- 3、达人标签增强算法prompt加两「行业」字段，语言地区标签要修改限制范围 -算法
- 4、商品分析 prompt，增加行业标签 -算法
- 5、匹配算法部分，用预算/达人数量=建联均价，获得预期的建联达人均值，在这个均值上下 10% 的报价，按照活跃度从高到低取值。如果数量不够则继续扩充百分比 -前端
- 6 后端-填入不合理建联预算和数量时，动态切换为无预算匹配-算法

# 达人报价与智能匹配优化

1. 需求概述

为了提升达人匹配精准度和用户体验，新增达人报价计算、智能预算分配、以及行业标签增强功能。通过自动化的报价体系和智能推荐，帮助用户更合理地分配营销预算，提高建联成功率。

1. 功能需求详情

### 2.1 达人报价体系构建

### 2.1.1 视频数据采集

- **数据范围**：采集每位达人最新5个视频的播放量、互动量数据
    - 互动量数据：YTB和 INS：点赞+评论数，Tiktok：点赞+评论+收藏
- **更新频率**：首次采集后，每周自动更新一次
- **展示位置**：在达人卡片和详情页显示预估报价区间

### 2.1.2 报价计算规则

- **计算公式**：CPM（每千次展示成本）× 最近5个视频平均播放量 ÷ 1000
- **报价展示**：以价格区间形式展示（如：$500-$1500）[500,1500]
- 获得 CPM 的值：根据行业、粉丝数范围、地区、平台四个字段进行取值

[](https://xcnjoit6imgy.feishu.cn/space/api/box/stream/download/asynccode/?code=Zjc2ZTdiZmNlNzk3ZmU3ZDVlM2QxZGU0OWQzYjEzOThfM3JKcDhHVGg4TUVQelFDSmVWQWFlNUY0T3RmeXFkOUtfVG9rZW46U1N2dGJ2VmkwbzRsSUx4Y29MM2M3YWx1bnhlXzE3NTI1Njk2NjA6MTc1MjU3MzI2MF9WNA)

### 2.2 标签体系增强

### 2.2.1 达人标签优化

- **新增/优化标签类型**：
    - 行业标签（如：电子产品、美妆护肤、运动健身等）
    - 语言标签（限定为主流语言：英语、西班牙语、法语等）
    - 地区标签（按大洲/主要国家分类）
    - enhancetags标签：所有博主都再过一遍 enhancetags，增加标签数量，提高命中率
- **展示方式**：在达人卡片上以彩色标签形式展示
- 达人标签算法优化prompt

```
# 角色
你是一名多平台社交媒体资深分析师，擅长依据内容数据洞察达人特征、受众画像与商业价值，并给出跨平台综合评估。

# 任务
读取输入数据（单平台内容记录或多平台汇总指标），输出包含 25 个字段的完整 JSON 分析报告。

# 输入格式
1) 单平台内容数据：JSON 数组，字段示例
   - content_title / promo_category / enhanced_tag / like_count / comment_count / views_number / publish_date / description / top_comments / content_type ...
2) 多平台汇总数据：平台、粉丝量、互动率、is_active_platform 及其他受众与内容特征。

# 输出格式（固定结构，不增删字段）
```json
{
  "platform": "",                 // 多平台时填“综合”
  "行业分类": "",                 // 美妆时尚/3C/游戏/户外/母婴/其他/无法判断
  "受众性别": "",                 // 男性为主/女性为主/均衡/无法判断
  "受众年龄": "",                 // 18-24岁/25-34岁/跨度较广/无法判断
  "地区": "",                     // 北美/日韩/中西欧/南美/东欧/东南亚/中东/其他/无法判断
  "语言": "",                     // 仅填一种：英语/西班牙语/法语/德语/俄语/中文/日语/韩语/阿拉伯语/葡萄牙语/其他/无法判断
  "内容格式": [""],               // 开箱测评/教程/Vlog/清单推荐/短剧/挑战/直播切片/无法判断
  "近期总结": "",                // 1-2 句；未知填“无法判断”
  "视频风格": "",                // 快节奏/电影感/简洁/原始/特效多/无法判断
  "内容调性": "",                // 专业/幽默/温馨/高级/生活流/可爱/二次元/无法判断
  "垂类深度": "",               // 高度垂直/中度垂直/泛领域/无法判断
  "推广能力": "",               // 频繁硬广/自然软广/测评推广/几乎无推广/无法判断
  "品牌重复": "",               // 深度绑定/品牌多样/单次推广/无商业推广/无法判断
  "展示场景": [""],              // 居家/工作室/户外/厨房/汽车/其他/无法判断
  "enhancedTags": [""],          // 从商品分类中选 3 个最匹配
  "粉丝国家": "",               // 如：中国/美国/日本/无法判断
  "粉丝语言": "",               // 同“语言”候选
  "粉丝购买力": "",             // 高/中/低/无法判断

  "核心内容方向": [""],          // ≤5 项
  "综合人设/风格": "",
  "主要受众画像": "",
  "商业化程度评估": "",         // 低度商业化/中度商业化/高度商业化/深度商业绑定
  "跨平台内容一致性": "",       // 高度一致/基本一致略有侧重/平台差异化明显/单平台
  "潜在合作品牌类型": [""],
  "达人总体评价": "",
  "带货能力评级": ""            // 高/中/低/无法判断
}
```

# 核心要求
1. 结论必须基于数据，无法确定时统一填“无法判断”。
2. 所有 25 个字段均需输出，字段名、顺序及 JSON 结构不可修改。
3. 单平台输入时，“跨平台内容一致性”填“单平台”。
4. 不输出多余说明，仅返回 JSON。

# 分析思路速览（仅供模型内部使用）
- 标题、标签、品类 ➜ 行业分类、内容格式、推广能力等
- 互动率、评论关键词 ➜ 受众性别/年龄/地区/购买力
- 多平台指标 ➜ 跨平台一致性、商业化程度
- 品牌出现频次 ➜ 品牌重复、推广能力
- 语言检测 ➜ 语言、粉丝语言
- 如无直接佐证，结果填“无法判断”。

商品分类选择范围：
Home Supplies
Kitchenware
Textiles & Soft Furnishings
Household Appliances
Womenswear & Underwear
Muslim Fashion
Shoes
Beauty & Personal Care
Phones & Electronics
Computers & Office Equipment
Pet Supplies
Baby & Maternity
Sports & Outdoor
Toys & Hobbies
Furniture
Tools & Hardware
Home Improvement
Kids’ Fashion
Menswear & Underwear
Luggage & Bags
Collectibles
Jewelry Accessories & Derivatives
Automotive & Motorcycle
Fashion Accessories
Food & Beverages
Health
Books, Magazines & Audio
Home Organizers
Bathroom Supplies
Home Decor
Home Care Supplies
Laundry Tools & Accessories
Festive & Party Supplies
Miscellaneous Home
Tea & Coffeeware
Kitchen Knives
Barbecue
Bar & Wine Utensils
Bakeware
Cookware
Cutlery & Tableware
Drinkware
Kitchen Utensils & Gadgets
Bedding and Linens
Household Textiles
Fabrics & Sewing Supplies
Kitchen Appliances
Home Appliances
Large Home Appliances
Commercial Appliances
Women’s Tops
Women’s Bottoms
Women’s Dresses
Women’s Special Clothing
Women’s Suits & Sets
Women’s Underwear
Women’s Sleepwear & Loungewear
Hijabs
Women’s Islamic Clothing
Men’s Islamic Clothing
Outerwear
Islamic Accessories
Prayer Attire & Equipment
Islamic Sportswear
Umroh Equipment
Women’s Shoes
Men’s Shoes
Shoe Accessories
Makeup
Skincare
Haircare & Styling
Hand, Foot & Nail Care
Bath & Body Care
Men’s Care
Personal Care Appliances
Eye & Ear Care
Nasal & Oral Care
Special Personal Care
Mobile Phone Accessories
Cameras & Photography
Audio & Video
Gaming & Consoles
Smart & Wearable Devices
Universal Accessories
Tablet & Computer Accessories
Desktop Computers, Laptops & Tablets
Desktop & Laptop Components
Computer Accessories
Data Storage & Software
Network Components
Office Equipment
Office Stationery & Supplies
Dog & Cat Food
Dog & Cat Furniture
Dog & Cat Clothing
Dog & Cat Litter
Dog & Cat Grooming
Dog & Cat Healthcare
Dog & Cat Accessories
Fish & Aquatic Supplies
Reptile & Amphibian Supplies
Bird Supplies
Small Animal Supplies
Farm Animal & Poultry Supplies
Baby Clothing & Shoes
Nursing & Feeding
Baby Care & Health
Maternity Supplies
Baby Fashion Accessories
Sport & Outdoor Clothing
Sports Footwear
Sports & Outdoor Accessories
Ball Sports
Water Sports
Winter Sports
Fitness
Camping & Hiking
Leisure & Outdoor Recreation Equipment
Swimwear, Surfwear & Wetsuits
Fan Shop
Lawn Games
Educational Toys
Games & Puzzles
Classic & Novelty Toys
Musical Instruments & Accessories
DIY
Indoor Furniture
Outdoor Furniture
Commercial Furniture
Power Tools
Hand Tools
Measuring Tools
Garden Tools
Soldering Equipment
Tool Organizers
Hardware
Pumps & Plumbing
Solar & Wind Power
Lights & Lighting
Electrical Equipment & Supplies
Kitchen Fixtures
Building Supplies
Bathroom Fixtures
Security & Safety
Garden Supplies
Boys’ Clothes
Girls’ Clothes
Men’s Tops
Men’s Bottoms
Men’s Special Occasion Clothing
Men’s Underwear & Socks
Men’s Sleepwear & Loungewear
Men’s Suits & Sets
Women’s Bags
Men’s Bags
Luggage & Travel Bags
Functional Bags
Bag Accessories
Cultural Items
Trading Cards & Accessories
Sports Collectibles
Entertainment
Platinum, Carat Gold
Silver
Natural Crystal
Non-natural Crystal
Jade
Semiprecious Stones
Artificial Gemstones
Pearl
Amber
Mellite
Auto Replacement Parts
Motorcycle Parts
Car Electronics
Car Exterior Accessories
Car Interior Accessories
Car Repair Tools
Car Lights
Quads, Motorhomes & Boats
Car Washing & Maintenance
Motorcycle Accessories
Hair Extensions & Wigs
Dressmaking Fabrics
Wedding Accessories
Clothes Accessories
Eyewear
Fashion Watches & Accessories
Costume Jewelry & Accessories
Hair Accessories
Milk & Dairy
Beer, Wine & Spirits
Drinks
Pantry Food
Staples & Cooking Essentials
Baking
Snacks
Nutrition & Wellness
Medical Supplies
Alternative Medications & Treatments
Humanities & Social Sciences
Magazines & Newspapers
Literature & Art
Economics & Management
Children’s & Infants’ Books
Science & Technology
Lifestyle & Hobbies
Education & Schooling
Video & Music

```

### 2.2.2 商品行业标签

- **自动识别**：AI分析商品时自动生成行业标签
- **enhancetags标签**：所有商品默认打上 3 个enhancetags，增加标签数量，提高命中率和筛选池大小
- **标签展示**：在商品分析结果卡片中显示
- **可编辑**：用户可手动修改或添加行业标签

```
Role: 资深电商商品分析师

Task: 判断商品有效性并提取结构化标签

Input: 商品JSON，关键字段：
ProductName, Brand, AmazonCategory, Specifications, Description, Price, Rating, ReviewCount

Process:
1. 验证商品有效性
2. 有效则提取标签，无效则返回原因

有效性检查:
- 数据异常: 全同字符/测试数据/价格异常(≤0或离谱)
- 内容缺失: ProductName空/关键字段均空
- 逻辑异常: 名称描述不符/分类错误
标签提取规则:
- 有信息则提取，无信息则留空[]
- 基于实际内容，不推测

Output JSON:
{
  "IsValidProduct": true/false,
  "Industry": "美妆时尚/3C/游戏/户外/母婴/其他",
  "FeatureTags": ["功能/规格/材质/设计"],
  "AudienceTags": ["目标人群"],
  "UsageScenarioTags": ["使用场景"],
  "enhancetags": ["适配的商品分类1-3个"],
  "InvalidReason": "仅无效时：明确告知问题字段"
}

标签规范:
FeatureTags: 大容量存储(64GB), 智能降噪, 超长续航(100h)
AudienceTags: 记者, 学生, 内容创作者
UsageScenarioTags: 会议记录, 讲座录音, 便携拍摄
enhancetags: 从以下分类选择最相关的1-3个：
[Home Supplies
Kitchenware
Textiles & Soft Furnishings
Household Appliances
Womenswear & Underwear
Muslim Fashion
Shoes
Beauty & Personal Care
Phones & Electronics
Computers & Office Equipment
Pet Supplies
Baby & Maternity
Sports & Outdoor
Toys & Hobbies
Furniture
Tools & Hardware
Home Improvement
Kids’ Fashion
Menswear & Underwear
Luggage & Bags
Collectibles
Jewelry Accessories & Derivatives
Automotive & Motorcycle
Fashion Accessories
Food & Beverages
Health
Books, Magazines & Audio
Home Organizers
Bathroom Supplies
Home Decor
Home Care Supplies
Laundry Tools & Accessories
Festive & Party Supplies
Miscellaneous Home
Tea & Coffeeware
Kitchen Knives
Barbecue
Bar & Wine Utensils
Bakeware
Cookware
Cutlery & Tableware
Drinkware
Kitchen Utensils & Gadgets
Bedding and Linens
Household Textiles
Fabrics & Sewing Supplies
Kitchen Appliances
Home Appliances
Large Home Appliances
Commercial Appliances
Women’s Tops
Women’s Bottoms
Women’s Dresses
Women’s Special Clothing
Women’s Suits & Sets
Women’s Underwear
Women’s Sleepwear & Loungewear
Hijabs
Women’s Islamic Clothing
Men’s Islamic Clothing
Outerwear
Islamic Accessories
Prayer Attire & Equipment
Islamic Sportswear
Umroh Equipment
Women’s Shoes
Men’s Shoes
Shoe Accessories
Makeup
Skincare
Haircare & Styling
Hand, Foot & Nail Care
Bath & Body Care
Men’s Care
Personal Care Appliances
Eye & Ear Care
Nasal & Oral Care
Special Personal Care
Mobile Phone Accessories
Cameras & Photography
Audio & Video
Gaming & Consoles
Smart & Wearable Devices
Universal Accessories
Tablet & Computer Accessories
Desktop Computers, Laptops & Tablets
Desktop & Laptop Components
Computer Accessories
Data Storage & Software
Network Components
Office Equipment
Office Stationery & Supplies
Dog & Cat Food
Dog & Cat Furniture
Dog & Cat Clothing
Dog & Cat Litter
Dog & Cat Grooming
Dog & Cat Healthcare
Dog & Cat Accessories
Fish & Aquatic Supplies
Reptile & Amphibian Supplies
Bird Supplies
Small Animal Supplies
Farm Animal & Poultry Supplies
Baby Clothing & Shoes
Nursing & Feeding
Baby Care & Health
Maternity Supplies
Baby Fashion Accessories
Sport & Outdoor Clothing
Sports Footwear
Sports & Outdoor Accessories
Ball Sports
Water Sports
Winter Sports
Fitness
Camping & Hiking
Leisure & Outdoor Recreation Equipment
Swimwear, Surfwear & Wetsuits
Fan Shop
Lawn Games
Educational Toys
Games & Puzzles
Classic & Novelty Toys
Musical Instruments & Accessories
DIY
Indoor Furniture
Outdoor Furniture
Commercial Furniture
Power Tools
Hand Tools
Measuring Tools
Garden Tools
Soldering Equipment
Tool Organizers
Hardware
Pumps & Plumbing
Solar & Wind Power
Lights & Lighting
Electrical Equipment & Supplies
Kitchen Fixtures
Building Supplies
Bathroom Fixtures
Security & Safety
Garden Supplies
Boys’ Clothes
Girls’ Clothes
Men’s Tops
Men’s Bottoms
Men’s Special Occasion Clothing
Men’s Underwear & Socks
Men’s Sleepwear & Loungewear
Men’s Suits & Sets
Women’s Bags
Men’s Bags
Luggage & Travel Bags
Functional Bags
Bag Accessories
Cultural Items
Trading Cards & Accessories
Sports Collectibles
Entertainment
Platinum, Carat Gold
Silver
Natural Crystal
Non-natural Crystal
Jade
Semiprecious Stones
Artificial Gemstones
Pearl
Amber
Mellite
Auto Replacement Parts
Motorcycle Parts
Car Electronics
Car Exterior Accessories
Car Interior Accessories
Car Repair Tools
Car Lights
Quads, Motorhomes & Boats
Car Washing & Maintenance
Motorcycle Accessories
Hair Extensions & Wigs
Dressmaking Fabrics
Wedding Accessories
Clothes Accessories
Eyewear
Fashion Watches & Accessories
Costume Jewelry & Accessories
Hair Accessories
Milk & Dairy
Beer, Wine & Spirits
Drinks
Pantry Food
Staples & Cooking Essentials
Baking
Snacks
Nutrition & Wellness
Medical Supplies
Alternative Medications & Treatments
Humanities & Social Sciences
Magazines & Newspapers
Literature & Art
Economics & Management
Children’s & Infants’ Books
Science & Technology
Lifestyle & Hobbies
Education & Schooling
Video & Music]

注意:
- 有信息则提取，无信息则留空[] ，不输出其他内容
-有效时进行合理推测相关内容
- InvalidReason需明确指出问题字段，20字内
- 仅返回JSON，无额外文字

```

### 2.3 智能预算分配

### 2.3.1 达人智能筛选

- **触发时机**：用户设置营销预算和建联数量后
- **筛选逻辑**：
    - 计算单个达人平均预算（总预算÷建联数量）
    - 从 3个enhancetags的达人池子里获取达人，不管数量
    - 筛选报价在平均预算±20%范围内的达人
    - 若数量不足，去掉预算标签按活跃度获取进行匹配
    - 按达人活跃度从高到低排序，获取算法上限的人数（目前 50 人）
- **结果展示**：显示"已为您匹配X位符合预算的达人"

### 2.3.2 动态建联数量推荐

- 问题：粉丝数、达人播放量都不知道，算不出达人的报价范围
- 办法：直接写死一个逻辑，每个行业有一个报价的上下限

```
const industryAvgCost = {
  "美妆时尚": { min: 800, max: 2000 },    // CPM 35-70，中小达人居多
  "3C": { min: 1000, max: 2500 },         // CPM 20-40，科技达人规模较大
  "游戏": { min: 600, max: 1500 },        // CPM 15-30，受众集中
  "户外": { min: 500, max: 1200 },        // CPM 25-50，垂直领域
  "母婴": { min: 700, max: 1800 },        // CPM 30-60，信任度要求高
  "其他": { min: 500, max: 1500 }         // 默认中间值
};

```

- **交互设计**：
    - 用户输入营销总预算后，建联数量输入框下方动态显示推荐范围，按照输入的数字，和已知该商品的行业，除出来对应的推荐数量
        - 例如 10000 预算，推广 3c产品，即 10000/1000-10000/2500=4-10 的达人
    - 显示文案："基于您的预算，建议建联 8-15 位达人"
- **计算逻辑**：
    - 根据预算金额和这个行业的达人平均报价计算合理区间
- **实时更新**：预算金额变化时，推荐范围实时更新

### 2.4 界面交互优化

### 2.4.1 营销活动设置界面更新

- **预算输入优化**：
    - 输入框增加货币符号前缀"$"
    - 输入时显示千位分隔符（如：$10,000）
    - 下方显示参考文案："该预算可覆盖中等规模达人10-20位"

### 2.4.2 达人列表增强显示

- **新增信息展示**：
    - 预估报价区间（如：$800-$1,200）
    - 活跃度指标（最近7天发布视频数）
    - 行业标签（最多显示3个）
- **筛选器更新**：
    - 新增按报价区间筛选
    - 新增按行业标签筛选
    - 新增按活跃度排序

### 2.4.3 匹配结果优化

- **预算匹配度展示**：
    - 在推荐达人卡片上显示"预算匹配度"百分比
    - 完全匹配显示绿色，部分匹配显示橙色
- **超预算提醒**：
    - 若达人报价超出平均预算20%，显示提示标签"超预算"

### 2.5 数据展示规范

### 2.5.1 报价显示规则

- 所有报价统一使用美元符号"$"
- 数值超过1000时使用"k"简写（如：$1.5k）
- 报价区间始终显示最小值-最大值

### 2.5.2 更新状态提示

- 报价数据显示更新时间（如："3天前更新"）
- 超过14天未更新的数据显示警告图标
- 提供手动刷新按钮
1. 用户价值
- **预算透明化**：用户清晰了解每位达人的大致报价，合理分配预算
- **匹配精准化**：通过行业标签和报价筛选，找到最适合的达人
- **决策智能化**：系统根据预算智能推荐建联数量和达人列表，降低决策成本